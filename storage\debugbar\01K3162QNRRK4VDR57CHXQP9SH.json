{"__meta": {"id": "01K3162QNRRK4VDR57CHXQP9SH", "datetime": "2025-08-19 18:20:17", "utime": **********.913331, "method": "GET", "uri": "/california-gold-nutrition-sport-creatine-monohydrate-unflavored-1-lb-454-g", "ip": "127.0.0.1"}, "modules": {"count": 7, "modules": [{"name": "Webkul\\Attribute", "models": ["Webkul\\Attribute\\Models\\Attribute (31)", "Webkul\\Attribute\\Models\\AttributeFamily (2)", "Webkul\\Attribute\\Models\\AttributeTranslation (1)", "Webkul\\Attribute\\Models\\AttributeOption (3)"], "views": [], "queries": [{"sql": "select * from `attributes` where `code` = 'url_key'", "duration": 1.01, "duration_str": "1.01s", "connection": "mditems"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "duration": 0.93, "duration_str": "930ms", "connection": "mditems"}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 1", "duration": 1.6, "duration_str": "1.6s", "connection": "mditems"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "duration": 0.83, "duration_str": "830ms", "connection": "mditems"}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 1 and `attributes`.`is_visible_on_front` = 1", "duration": 1.34, "duration_str": "1.34s", "connection": "mditems"}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` = 25 and `attribute_translations`.`attribute_id` is not null", "duration": 4.23, "duration_str": "4.23s", "connection": "mditems"}, {"sql": "select * from `attributes` where `attributes`.`id` = 33 limit 1", "duration": 0.97, "duration_str": "970ms", "connection": "mditems"}, {"sql": "select * from `attribute_options` where `attribute_id` = 33", "duration": 0.88, "duration_str": "880ms", "connection": "mditems"}]}, {"name": "Webkul\\Category", "models": [], "views": [], "queries": [{"sql": "select * from `categories` where exists (select * from `category_translations` where `categories`.`id` = `category_translations`.`category_id` and `category_translations`.`slug` = 'california-gold-nutrition-sport-creatine-monohydrate-unflavored-1-lb-454-g') limit 1", "duration": 1.24, "duration_str": "1.24s", "connection": "mditems"}]}, {"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (2)", "Webkul\\Core\\Models\\CoreConfig (3)", "Webkul\\Core\\Models\\Locale (7)", "Webkul\\Core\\Models\\Currency (3)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('127.0.0.1:8000', 'http://127.0.0.1:8000', 'https://127.0.0.1:8000')", "duration": 0.75, "duration_str": "750ms", "connection": "mditems"}, {"sql": "select * from `channels` limit 1", "duration": 0.73, "duration_str": "730ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'marketplace.settings.general.status' and `channel_code` = 'default'", "duration": 0.75, "duration_str": "750ms", "connection": "mditems"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.75, "duration_str": "750ms", "connection": "mditems"}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "duration": 0.64, "duration_str": "640ms", "connection": "mditems"}, {"sql": "select * from `currencies` where `code` = 'USD'", "duration": 0.56, "duration_str": "560ms", "connection": "mditems"}, {"sql": "select * from `locales`", "duration": 1.03, "duration_str": "1.03s", "connection": "mditems"}, {"sql": "select * from `channels` where `code` = 'default'", "duration": 0.7, "duration_str": "700ms", "connection": "mditems"}, {"sql": "select * from `locales` where `locales`.`id` = 1 limit 1", "duration": 0.68, "duration_str": "680ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'catalog.rich_snippets.products.enable'", "duration": 1.11, "duration_str": "1.11s", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'general.general.breadcrumbs.shop'", "duration": 0.66, "duration_str": "660ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'catalog.products.review.guest_review'", "duration": 0.72, "duration_str": "720ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'catalog.products.review.summary'", "duration": 1.06, "duration_str": "1.06s", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'catalog.products.review.customer_review'", "duration": 0.64, "duration_str": "640ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.review_translation.enabled' and `channel_code` = 'default'", "duration": 1.11, "duration_str": "1.11s", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'customer.settings.wishlist.wishlist_option'", "duration": 0.78, "duration_str": "780ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'catalog.products.settings.compare_option'", "duration": 0.68, "duration_str": "680ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'sales.checkout.shopping_cart.cart_page'", "duration": 1.11, "duration_str": "1.11s", "connection": "mditems"}, {"sql": "select * from `currency_exchange_rates` where `target_currency` = 1", "duration": 0.96, "duration_str": "960ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'sales.taxes.calculation.product_prices'", "duration": 0.93, "duration_str": "930ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'catalog.products.storefront.buy_now_button_display'", "duration": 1.12, "duration_str": "1.12s", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'catalog.products.social_share.enabled'", "duration": 1.18, "duration_str": "1.18s", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'marketplace.settings.product.flag_enabled' and `channel_code` = 'default'", "duration": 0.64, "duration_str": "640ms", "connection": "mditems"}, {"sql": "select * from `locales` where `code` = 'en'", "duration": 0.87, "duration_str": "870ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'general.content.custom_scripts.custom_css' and `channel_code` = 'default'", "duration": 1.49, "duration_str": "1.49s", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'general.content.speculation_rules.enabled'", "duration": 0.85, "duration_str": "850ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'general.content.speculation_rules.prerender_enabled'", "duration": 1.11, "duration_str": "1.11s", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'general.content.speculation_rules.prerender_eagerness'", "duration": 0.67, "duration_str": "670ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'general.content.speculation_rules.prerender_ignore_urls'", "duration": 0.67, "duration_str": "670ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'general.content.speculation_rules.prerender_ignore_url_params'", "duration": 0.65, "duration_str": "650ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'general.content.speculation_rules.prefetch_enabled'", "duration": 0.9, "duration_str": "900ms", "connection": "mditems"}, {"sql": "select count(*) as aggregate from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 1.42, "duration_str": "1.42s", "connection": "mditems"}, {"sql": "select count(*) as aggregate from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "duration": 2.04, "duration_str": "2.04s", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'general.content.header_offer.title'", "duration": 0.77, "duration_str": "770ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'general.content.header_offer.redirection_link'", "duration": 0.68, "duration_str": "680ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'general.content.header_offer.redirection_title'", "duration": 0.65, "duration_str": "650ms", "connection": "mditems"}, {"sql": "select `name` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 and `code` = 'en' order by `name` asc limit 1", "duration": 0.56, "duration_str": "560ms", "connection": "mditems"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 order by `name` asc", "duration": 1.09, "duration_str": "1.09s", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'catalog.products.search.min_query_length'", "duration": 0.6, "duration_str": "600ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'catalog.products.search.max_query_length'", "duration": 0.81, "duration_str": "810ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'catalog.products.settings.image_search'", "duration": 0.71, "duration_str": "710ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'sales.checkout.mini_cart.display_mini_cart'", "duration": 0.88, "duration_str": "880ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'sales.checkout.my_cart.summary'", "duration": 0.62, "duration_str": "620ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'sales.checkout.mini_cart.offer_info'", "duration": 0.69, "duration_str": "690ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'sales.taxes.shopping_cart.display_prices'", "duration": 0.65, "duration_str": "650ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'sales.taxes.shopping_cart.display_subtotal'", "duration": 0.69, "duration_str": "690ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'general.design.categories.category_view'", "duration": 0.54, "duration_str": "540ms", "connection": "mditems"}, {"sql": "select count(*) as aggregate from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.68, "duration_str": "680ms", "connection": "mditems"}, {"sql": "select count(*) as aggregate from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "duration": 0.42, "duration_str": "420ms", "connection": "mditems"}, {"sql": "select `name` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 and `code` = 'en' order by `name` asc limit 1", "duration": 0.51, "duration_str": "510ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'general.gdpr.settings.enabled' and `channel_code` = 'default' and `locale_code` = 'en'", "duration": 0.55, "duration_str": "550ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'customer.settings.newsletter.subscription'", "duration": 0.58, "duration_str": "580ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'general.content.custom_scripts.custom_javascript' and `channel_code` = 'default'", "duration": 0.86, "duration_str": "860ms", "connection": "mditems"}]}, {"name": "Webkul\\Customer", "models": ["Webkul\\Customer\\Models\\CustomerGroup (1)"], "views": [], "queries": [{"sql": "select * from `customer_groups` where `code` = 'guest'", "duration": 1.87, "duration_str": "1.87s", "connection": "mditems"}]}, {"name": "Webkul\\Product", "models": ["Webkul\\Product\\Models\\ProductAttributeValue (57)", "Webkul\\Product\\Models\\Product (1)", "Webkul\\Product\\Models\\ProductImage (1)", "Webkul\\Product\\Models\\ProductPriceIndex (3)", "Webkul\\Product\\Models\\ProductInventoryIndex (1)"], "views": [], "queries": [{"sql": "select * from `product_attribute_values` where `attribute_id` = 3 and `text_value` = 'california-gold-nutrition-sport-creatine-monohydrate-unflavored-1-lb-454-g'", "duration": 1.07, "duration_str": "1.07s", "connection": "mditems"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 2 and `product_attribute_values`.`product_id` is not null", "duration": 1.36, "duration_str": "1.36s", "connection": "mditems"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 2 and `product_attribute_values`.`product_id` is not null", "duration": 1.3, "duration_str": "1.3s", "connection": "mditems"}, {"sql": "select * from `product_reviews` where `product_reviews`.`product_id` = 2 and `product_reviews`.`product_id` is not null", "duration": 0.79, "duration_str": "790ms", "connection": "mditems"}, {"sql": "select `rating`, count(*) as total from `product_reviews` where `product_reviews`.`product_id` = 2 and `product_reviews`.`product_id` is not null and `status` = 'approved' group by `rating` order by `rating` desc", "duration": 1.12, "duration_str": "1.12s", "connection": "mditems"}, {"sql": "select * from `product_images` where `product_images`.`product_id` = 2 and `product_images`.`product_id` is not null order by `position` asc", "duration": 2.01, "duration_str": "2.01s", "connection": "mditems"}, {"sql": "select * from `product_videos` where `product_videos`.`product_id` = 2 and `product_videos`.`product_id` is not null order by `position` asc", "duration": 0.97, "duration_str": "970ms", "connection": "mditems"}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` = 2 and `product_price_indices`.`product_id` is not null", "duration": 0.97, "duration_str": "970ms", "connection": "mditems"}, {"sql": "select * from `product_customer_group_prices` where `product_customer_group_prices`.`product_id` = 2 and `product_customer_group_prices`.`product_id` is not null and (`customer_group_id` = 1 or `customer_group_id` is null) and `qty` > 1 group by `qty` order by `qty` asc", "duration": 4.34, "duration_str": "4.34s", "connection": "mditems"}, {"sql": "select * from `product_grouped_products` where `associated_product_id` = 2 limit 1", "duration": 3.04, "duration_str": "3.04s", "connection": "mditems"}, {"sql": "select * from `product_bundle_option_products` where `product_id` = 2 limit 1", "duration": 3.41, "duration_str": "3.41s", "connection": "mditems"}, {"sql": "select * from `product_customizable_options` where `product_customizable_options`.`product_id` = 2 and `product_customizable_options`.`product_id` is not null order by `sort_order` asc", "duration": 3.03, "duration_str": "3.03s", "connection": "mditems"}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` = 2 and `product_inventory_indices`.`product_id` is not null", "duration": 0.9, "duration_str": "900ms", "connection": "mditems"}]}, {"name": "Webkul\\Theme", "models": ["Webkul\\Theme\\Models\\ThemeCustomization (2)", "Webkul\\Theme\\Models\\ThemeCustomizationTranslation (2)"], "views": [], "queries": [{"sql": "select * from `theme_customizations` where `type` = 'services_content' and `status` = 1 and `theme_code` = 'default' and `channel_id` = 1", "duration": 0.5, "duration_str": "500ms", "connection": "mditems"}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (12)", "duration": 0.52, "duration_str": "520ms", "connection": "mditems"}, {"sql": "select * from `theme_customizations` where `type` = 'footer_links' and `status` = 1 and `theme_code` = 'default' and `channel_id` = 1", "duration": 0.58, "duration_str": "580ms", "connection": "mditems"}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (11)", "duration": 0.63, "duration_str": "630ms", "connection": "mditems"}]}, {"name": "Webkul\\Marketplace", "models": ["Webkul\\Marketplace\\Models\\Product\\Product (1)", "Webkul\\Marketplace\\Models\\Product (5)", "Webkul\\Marketplace\\Models\\Seller (5)", "Webkul\\Marketplace\\Models\\Core\\Visit (1)", "Webkul\\Marketplace\\Models\\MpFlagReason (6)", "Webkul\\Marketplace\\Models\\MpFlagReasonTranslation (6)"], "views": [], "queries": [{"sql": "select * from `products` where `products`.`id` = 2 limit 1", "duration": 1.82, "duration_str": "1.82s", "connection": "mditems"}, {"sql": "select * from `marketplace_products` where `product_id` = 2 and `is_owner` = 1", "duration": 0.98, "duration_str": "980ms", "connection": "mditems"}, {"sql": "select * from `marketplace_sellers` where `marketplace_sellers`.`id` = 4 limit 1", "duration": 1.24, "duration_str": "1.24s", "connection": "mditems"}, {"sql": "select * from `products` where `products`.`id` = 2 limit 1", "duration": 0.89, "duration_str": "890ms", "connection": "mditems"}, {"sql": "select * from `visits` where (`method` = 'GET' and `url` = 'http://127.0.0.1:8000/california-gold-nutrition-sport-creatine-monohydrate-unflavored-1-lb-454-g' and `ip` = '127.0.0.1' and `visitor_id` is null and `visitor_type` is null and `channel_id` = 1) order by `created_at` desc limit 1", "duration": 1.91, "duration_str": "1.91s", "connection": "mditems"}, {"sql": "select * from `marketplace_products` where `product_id` = 2 and `is_owner` = 1", "duration": 0.87, "duration_str": "870ms", "connection": "mditems"}, {"sql": "select * from `marketplace_sellers` where `marketplace_sellers`.`id` in (4)", "duration": 0.61, "duration_str": "610ms", "connection": "mditems"}, {"sql": "select * from `marketplace_products` where `product_id` = 2 and `is_owner` = 1", "duration": 1.1, "duration_str": "1.1s", "connection": "mditems"}, {"sql": "select * from `marketplace_sellers` where `marketplace_sellers`.`id` = 4 limit 1", "duration": 1.05, "duration_str": "1.05s", "connection": "mditems"}, {"sql": "select * from `marketplace_products` where `product_id` = 2 and `is_owner` = 1", "duration": 0.68, "duration_str": "680ms", "connection": "mditems"}, {"sql": "select * from `marketplace_sellers` where `marketplace_sellers`.`id` = 4 limit 1", "duration": 0.73, "duration_str": "730ms", "connection": "mditems"}, {"sql": "select * from `marketplace_products` where `product_id` = 2 and `is_owner` = 1", "duration": 0.68, "duration_str": "680ms", "connection": "mditems"}, {"sql": "select * from `marketplace_sellers` where `marketplace_sellers`.`id` in (4)", "duration": 1.06, "duration_str": "1.06s", "connection": "mditems"}, {"sql": "select avg(`rating`) as aggregate from `marketplace_seller_reviews` where `marketplace_seller_reviews`.`marketplace_seller_id` = 4 and `marketplace_seller_reviews`.`marketplace_seller_id` is not null and `status` = 'approved'", "duration": 3.31, "duration_str": "3.31s", "connection": "mditems"}, {"sql": "select count(*) as aggregate from `marketplace_products` inner join `marketplace_sellers` on `marketplace_sellers`.`id` = `marketplace_products`.`marketplace_seller_id` where `marketplace_products`.`product_id` = 2 and `marketplace_sellers`.`is_suspended` = 0 and `marketplace_sellers`.`is_approved` = 1 and `marketplace_products`.`is_owner` = 0 and `marketplace_products`.`is_approved` = 1", "duration": 1.09, "duration_str": "1.09s", "connection": "mditems"}, {"sql": "select * from `mp_flag_reasons` where (`status` = 1 and `type` = 'product')", "duration": 9.69, "duration_str": "9.69s", "connection": "mditems"}, {"sql": "select * from `mp_flag_reason_translations` where `mp_flag_reason_translations`.`mp_flag_reason_id` = 1 and `mp_flag_reason_translations`.`mp_flag_reason_id` is not null", "duration": 1.67, "duration_str": "1.67s", "connection": "mditems"}, {"sql": "select * from `mp_flag_reason_translations` where `mp_flag_reason_translations`.`mp_flag_reason_id` = 2 and `mp_flag_reason_translations`.`mp_flag_reason_id` is not null", "duration": 0.6, "duration_str": "600ms", "connection": "mditems"}, {"sql": "select * from `mp_flag_reason_translations` where `mp_flag_reason_translations`.`mp_flag_reason_id` = 3 and `mp_flag_reason_translations`.`mp_flag_reason_id` is not null", "duration": 1.09, "duration_str": "1.09s", "connection": "mditems"}, {"sql": "select * from `mp_flag_reason_translations` where `mp_flag_reason_translations`.`mp_flag_reason_id` = 4 and `mp_flag_reason_translations`.`mp_flag_reason_id` is not null", "duration": 0.61, "duration_str": "610ms", "connection": "mditems"}, {"sql": "select * from `mp_flag_reason_translations` where `mp_flag_reason_translations`.`mp_flag_reason_id` = 5 and `mp_flag_reason_translations`.`mp_flag_reason_id` is not null", "duration": 0.71, "duration_str": "710ms", "connection": "mditems"}, {"sql": "select * from `mp_flag_reason_translations` where `mp_flag_reason_translations`.`mp_flag_reason_id` = 6 and `mp_flag_reason_translations`.`mp_flag_reason_id` is not null", "duration": 0.83, "duration_str": "830ms", "connection": "mditems"}]}]}, "messages": {"count": 3, "messages": [{"message": "[18:20:17] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.243075, "xdebug_link": null, "collector": "log"}, {"message": "[18:20:17] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Repositories\\ReviewRepository.php on line 52", "message_html": null, "is_string": false, "label": "warning", "time": **********.587111, "xdebug_link": null, "collector": "log"}, {"message": "[18:20:17] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Core.php on line 961", "message_html": null, "is_string": false, "label": "warning", "time": **********.729552, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755607816.642073, "end": **********.924197, "duration": 1.2821240425109863, "duration_str": "1.28s", "measures": [{"label": "Booting", "start": 1755607816.642073, "relative_start": 0, "end": **********.021536, "relative_end": **********.021536, "duration": 0.*****************, "duration_str": "379ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.02155, "relative_start": 0.*****************, "end": **********.924199, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "903ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.036292, "relative_start": 0.*****************, "end": **********.041855, "relative_end": **********.041855, "duration": 0.005563020706176758, "duration_str": "5.56ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.224145, "relative_start": 0.****************, "end": **********.911868, "relative_end": **********.911868, "duration": 0.****************, "duration_str": "688ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: shop::products.view", "start": **********.229609, "relative_start": 0.***************, "end": **********.229609, "relative_end": **********.229609, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.breadcrumbs.index", "start": **********.359832, "relative_start": 0.****************, "end": **********.359832, "relative_end": **********.359832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::partials.breadcrumbs", "start": **********.361829, "relative_start": 0.7197561264038086, "end": **********.361829, "relative_end": **********.361829, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.view", "start": **********.36288, "relative_start": 0.7208070755004883, "end": **********.36288, "relative_end": **********.36288, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.gallery", "start": **********.363933, "relative_start": 0.7218601703643799, "end": **********.363933, "relative_end": **********.363933, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.tabs.item", "start": **********.365221, "relative_start": 0.7231481075286865, "end": **********.365221, "relative_end": **********.365221, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.view.reviews", "start": **********.366472, "relative_start": 0.7243990898132324, "end": **********.366472, "relative_end": **********.366472, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.reviews.index", "start": **********.368692, "relative_start": 0.7266190052032471, "end": **********.368692, "relative_end": **********.368692, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.reviews.card", "start": **********.369675, "relative_start": 0.7276020050048828, "end": **********.369675, "relative_end": **********.369675, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.control", "start": **********.370918, "relative_start": 0.7288451194763184, "end": **********.370918, "relative_end": **********.370918, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.media.index", "start": **********.371941, "relative_start": 0.7298681735992432, "end": **********.371941, "relative_end": **********.371941, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.media.images.lazy", "start": **********.372921, "relative_start": 0.7308480739593506, "end": **********.372921, "relative_end": **********.372921, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.error", "start": **********.373497, "relative_start": 0.73142409324646, "end": **********.373497, "relative_end": **********.373497, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.index", "start": **********.373941, "relative_start": 0.7318680286407471, "end": **********.373941, "relative_end": **********.373941, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.label", "start": **********.374659, "relative_start": 0.732586145401001, "end": **********.374659, "relative_end": **********.374659, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.error", "start": **********.375095, "relative_start": 0.7330219745635986, "end": **********.375095, "relative_end": **********.375095, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.index", "start": **********.375412, "relative_start": 0.7333390712738037, "end": **********.375412, "relative_end": **********.375412, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.label", "start": **********.384397, "relative_start": 0.7423241138458252, "end": **********.384397, "relative_end": **********.384397, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.control", "start": **********.384939, "relative_start": 0.742866039276123, "end": **********.384939, "relative_end": **********.384939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.error", "start": **********.385682, "relative_start": 0.7436091899871826, "end": **********.385682, "relative_end": **********.385682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.index", "start": **********.386009, "relative_start": 0.7439360618591309, "end": **********.386009, "relative_end": **********.386009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.label", "start": **********.386299, "relative_start": 0.7442259788513184, "end": **********.386299, "relative_end": **********.386299, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.control", "start": **********.386697, "relative_start": 0.744624137878418, "end": **********.386697, "relative_end": **********.386697, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.error", "start": **********.388051, "relative_start": 0.7459781169891357, "end": **********.388051, "relative_end": **********.388051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.index", "start": **********.388865, "relative_start": 0.7467920780181885, "end": **********.388865, "relative_end": **********.388865, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.index", "start": **********.389674, "relative_start": 0.7476010322570801, "end": **********.389674, "relative_end": **********.389674, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.reviews.index", "start": **********.390604, "relative_start": 0.7485311031341553, "end": **********.390604, "relative_end": **********.390604, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.reviews.card", "start": **********.391359, "relative_start": 0.7492861747741699, "end": **********.391359, "relative_end": **********.391359, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.image-zoomer.index", "start": **********.424274, "relative_start": 0.7822010517120361, "end": **********.424274, "relative_end": **********.424274, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.tabs.item", "start": **********.427403, "relative_start": 0.785330057144165, "end": **********.427403, "relative_end": **********.427403, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.tabs.index", "start": **********.427791, "relative_start": 0.7857182025909424, "end": **********.427791, "relative_end": **********.427791, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.tabs.index", "start": **********.428861, "relative_start": 0.7867879867553711, "end": **********.428861, "relative_end": **********.428861, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.accordion.index", "start": **********.429551, "relative_start": 0.787477970123291, "end": **********.429551, "relative_end": **********.429551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.view.reviews", "start": **********.431889, "relative_start": 0.78981614112854, "end": **********.431889, "relative_end": **********.431889, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.reviews.index", "start": **********.432798, "relative_start": 0.7907249927520752, "end": **********.432798, "relative_end": **********.432798, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.reviews.card", "start": **********.43306, "relative_start": 0.7909870147705078, "end": **********.43306, "relative_end": **********.43306, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.accordion.index", "start": **********.433337, "relative_start": 0.7912640571594238, "end": **********.433337, "relative_end": **********.433337, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.carousel", "start": **********.433851, "relative_start": 0.7917780876159668, "end": **********.433851, "relative_end": **********.433851, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.carousel", "start": **********.43432, "relative_start": 0.7922470569610596, "end": **********.43432, "relative_end": **********.43432, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.cards.grid", "start": **********.43471, "relative_start": 0.7926371097564697, "end": **********.43471, "relative_end": **********.43471, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.card", "start": **********.435126, "relative_start": 0.793053150177002, "end": **********.435126, "relative_end": **********.435126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.media.images.lazy", "start": **********.435766, "relative_start": 0.7936930656433105, "end": **********.435766, "relative_end": **********.435766, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.ratings", "start": **********.43819, "relative_start": 0.7961170673370361, "end": **********.43819, "relative_end": **********.43819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.media.images.lazy", "start": **********.457738, "relative_start": 0.8156650066375732, "end": **********.457738, "relative_end": **********.457738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.ratings", "start": **********.464049, "relative_start": 0.8219761848449707, "end": **********.464049, "relative_end": **********.464049, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.button.index", "start": **********.466832, "relative_start": 0.8247590065002441, "end": **********.466832, "relative_end": **********.466832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.carousel", "start": **********.467326, "relative_start": 0.8252530097961426, "end": **********.467326, "relative_end": **********.467326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.cards.grid", "start": **********.467597, "relative_start": 0.825524091720581, "end": **********.467597, "relative_end": **********.467597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.carousel", "start": **********.467989, "relative_start": 0.825916051864624, "end": **********.467989, "relative_end": **********.467989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.carousel", "start": **********.468334, "relative_start": 0.826261043548584, "end": **********.468334, "relative_end": **********.468334, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.cards.grid", "start": **********.468571, "relative_start": 0.8264980316162109, "end": **********.468571, "relative_end": **********.468571, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: marketplace::shop.products.top-selling", "start": **********.469212, "relative_start": 0.827139139175415, "end": **********.469212, "relative_end": **********.469212, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.carousel", "start": **********.476496, "relative_start": 0.8344230651855469, "end": **********.476496, "relative_end": **********.476496, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.carousel", "start": **********.47688, "relative_start": 0.8348071575164795, "end": **********.47688, "relative_end": **********.47688, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.cards.grid", "start": **********.477121, "relative_start": 0.8350481986999512, "end": **********.477121, "relative_end": **********.477121, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.view.gallery", "start": **********.477663, "relative_start": 0.835590124130249, "end": **********.477663, "relative_end": **********.477663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.gallery", "start": **********.478405, "relative_start": 0.8363320827484131, "end": **********.478405, "relative_end": **********.478405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.view.gallery.desktop", "start": **********.47892, "relative_start": 0.8368470668792725, "end": **********.47892, "relative_end": **********.47892, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.view.gallery.mobile", "start": **********.479754, "relative_start": 0.8376810550689697, "end": **********.479754, "relative_end": **********.479754, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.gallery", "start": **********.480363, "relative_start": 0.8382899761199951, "end": **********.480363, "relative_end": **********.480363, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.image-zoomer.index", "start": **********.480583, "relative_start": 0.8385100364685059, "end": **********.480583, "relative_end": **********.480583, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.index", "start": **********.501239, "relative_start": 0.859166145324707, "end": **********.501239, "relative_end": **********.501239, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.view.types.simple", "start": **********.515481, "relative_start": 0.8734080791473389, "end": **********.515481, "relative_end": **********.515481, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.view.customizable-options", "start": **********.516101, "relative_start": 0.8740279674530029, "end": **********.516101, "relative_end": **********.516101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.view.types.configurable", "start": **********.531899, "relative_start": 0.8898260593414307, "end": **********.531899, "relative_end": **********.531899, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.view.types.grouped", "start": **********.533945, "relative_start": 0.8918721675872803, "end": **********.533945, "relative_end": **********.533945, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.view.types.bundle", "start": **********.534909, "relative_start": 0.8928360939025879, "end": **********.534909, "relative_end": **********.534909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.view.types.downloadable", "start": **********.536005, "relative_start": 0.8939321041107178, "end": **********.536005, "relative_end": **********.536005, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.view.types.booking", "start": **********.536907, "relative_start": 0.894834041595459, "end": **********.536907, "relative_end": **********.536907, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.quantity-changer.index", "start": **********.538105, "relative_start": 0.8960320949554443, "end": **********.538105, "relative_end": **********.538105, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.button.index", "start": **********.558629, "relative_start": 0.9165561199188232, "end": **********.558629, "relative_end": **********.558629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: social_share::share", "start": **********.569742, "relative_start": 0.9276690483093262, "end": **********.569742, "relative_end": **********.569742, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: marketplace::shop.products.product-sellers", "start": **********.576331, "relative_start": 0.934257984161377, "end": **********.576331, "relative_end": **********.576331, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: marketplace::components.shop.shimmer.ratings", "start": **********.590295, "relative_start": 0.9482221603393555, "end": **********.590295, "relative_end": **********.590295, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: marketplace::components.shop.products.star-rating", "start": **********.591057, "relative_start": 0.9489841461181641, "end": **********.591057, "relative_end": **********.591057, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: marketplace::components.seller.shimmer.ratings", "start": **********.59206, "relative_start": 0.9499871730804443, "end": **********.59206, "relative_end": **********.59206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: marketplace::shop.products.report", "start": **********.59258, "relative_start": 0.9505071640014648, "end": **********.59258, "relative_end": **********.59258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.label", "start": **********.611536, "relative_start": 0.9694631099700928, "end": **********.611536, "relative_end": **********.611536, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.control", "start": **********.675183, "relative_start": 1.0331101417541504, "end": **********.675183, "relative_end": **********.675183, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.control", "start": **********.675912, "relative_start": 1.0338389873504639, "end": **********.675912, "relative_end": **********.675912, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.index", "start": **********.676578, "relative_start": 1.0345051288604736, "end": **********.676578, "relative_end": **********.676578, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.button.index", "start": **********.676864, "relative_start": 1.0347909927368164, "end": **********.676864, "relative_end": **********.676864, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.modal.index", "start": **********.677132, "relative_start": 1.0350589752197266, "end": **********.677132, "relative_end": **********.677132, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.index", "start": **********.678051, "relative_start": 1.035978078842163, "end": **********.678051, "relative_end": **********.678051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.index", "start": **********.68852, "relative_start": 1.0464470386505127, "end": **********.68852, "relative_end": **********.68852, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.index", "start": **********.688997, "relative_start": 1.046924114227295, "end": **********.688997, "relative_end": **********.688997, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: marketplace::components.shop.layouts.header.style", "start": **********.69519, "relative_start": 1.053117036819458, "end": **********.69519, "relative_end": **********.69519, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.flash-group.index", "start": **********.735208, "relative_start": 1.093135118484497, "end": **********.735208, "relative_end": **********.735208, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.flash-group.item", "start": **********.735756, "relative_start": 1.0936830043792725, "end": **********.735756, "relative_end": **********.735756, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.flash-group.item", "start": **********.736107, "relative_start": 1.094034194946289, "end": **********.736107, "relative_end": **********.736107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.modal.confirm", "start": **********.73658, "relative_start": 1.0945069789886475, "end": **********.73658, "relative_end": **********.73658, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.header.index", "start": **********.737018, "relative_start": 1.0949451923370361, "end": **********.737018, "relative_end": **********.737018, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.header.desktop.top", "start": **********.74421, "relative_start": 1.1021370887756348, "end": **********.74421, "relative_end": **********.74421, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.dropdown.index", "start": **********.745132, "relative_start": 1.1030590534210205, "end": **********.745132, "relative_end": **********.745132, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.dropdown.index", "start": **********.764032, "relative_start": 1.1219589710235596, "end": **********.764032, "relative_end": **********.764032, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.header.desktop.index", "start": **********.768077, "relative_start": 1.1260039806365967, "end": **********.768077, "relative_end": **********.768077, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.header.desktop.bottom", "start": **********.768572, "relative_start": 1.1264991760253906, "end": **********.768572, "relative_end": **********.768572, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::search.images.index", "start": **********.790547, "relative_start": 1.1484739780426025, "end": **********.790547, "relative_end": **********.790547, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::checkout.cart.mini-cart", "start": **********.795181, "relative_start": 1.1531081199645996, "end": **********.795181, "relative_end": **********.795181, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.quantity-changer.index", "start": **********.81358, "relative_start": 1.1715071201324463, "end": **********.81358, "relative_end": **********.81358, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.drawer.index", "start": **********.814775, "relative_start": 1.1727020740509033, "end": **********.814775, "relative_end": **********.814775, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: marketplace::components.shop.layouts.header.sell", "start": **********.830563, "relative_start": 1.1884901523590088, "end": **********.830563, "relative_end": **********.830563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: marketplace::components.shop.layouts.header.profile", "start": **********.833425, "relative_start": 1.191352128982544, "end": **********.833425, "relative_end": **********.833425, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.dropdown.index", "start": **********.83423, "relative_start": 1.1921570301055908, "end": **********.83423, "relative_end": **********.83423, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.drawer.index", "start": **********.840714, "relative_start": 1.198641061782837, "end": **********.840714, "relative_end": **********.840714, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.header.mobile.index", "start": **********.841421, "relative_start": 1.199347972869873, "end": **********.841421, "relative_end": **********.841421, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::checkout.cart.mini-cart", "start": **********.84837, "relative_start": 1.2062971591949463, "end": **********.84837, "relative_end": **********.84837, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: marketplace::components.shop.layouts.header.sell", "start": **********.849006, "relative_start": 1.2069330215454102, "end": **********.849006, "relative_end": **********.849006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.dropdown.index", "start": **********.851539, "relative_start": 1.2094659805297852, "end": **********.851539, "relative_end": **********.851539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::search.images.index", "start": **********.85438, "relative_start": 1.2123069763183594, "end": **********.85438, "relative_end": **********.85438, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: marketplace::components.shop.layouts.header.profile", "start": **********.855244, "relative_start": 1.2131710052490234, "end": **********.855244, "relative_end": **********.855244, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.drawer.index", "start": **********.859837, "relative_start": 1.217764139175415, "end": **********.859837, "relative_end": **********.859837, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.drawer.index", "start": **********.862534, "relative_start": 1.220461130142212, "end": **********.862534, "relative_end": **********.862534, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.drawer.index", "start": **********.862957, "relative_start": 1.220884084701538, "end": **********.862957, "relative_end": **********.862957, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.services", "start": **********.868872, "relative_start": 1.2267990112304688, "end": **********.868872, "relative_end": **********.868872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.footer.index", "start": **********.878005, "relative_start": 1.2359321117401123, "end": **********.878005, "relative_end": **********.878005, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.accordion.index", "start": **********.89818, "relative_start": 1.2561070919036865, "end": **********.89818, "relative_end": **********.89818, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.control", "start": **********.903794, "relative_start": 1.261721134185791, "end": **********.903794, "relative_end": **********.903794, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.error", "start": **********.904557, "relative_start": 1.262484073638916, "end": **********.904557, "relative_end": **********.904557, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.index", "start": **********.904875, "relative_start": 1.2628021240234375, "end": **********.904875, "relative_end": **********.904875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core::blade.tracer.style", "start": **********.905463, "relative_start": 1.263390064239502, "end": **********.905463, "relative_end": **********.905463, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: paypal::checkout.onepage.paypal-smart-button", "start": **********.905816, "relative_start": 1.2637431621551514, "end": **********.905816, "relative_end": **********.905816, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 51406096, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 122, "nb_templates": 122, "templates": [{"name": "1x shop::products.view", "param_count": null, "params": [], "start": **********.229394, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.phpshop::products.view", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview.blade.php&line=1", "ajax": false, "filename": "view.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::products.view"}, {"name": "1x shop::components.breadcrumbs.index", "param_count": null, "params": [], "start": **********.359774, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/breadcrumbs/index.blade.phpshop::components.breadcrumbs.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fbreadcrumbs%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.breadcrumbs.index"}, {"name": "1x shop::partials.breadcrumbs", "param_count": null, "params": [], "start": **********.361779, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/partials/breadcrumbs.blade.phpshop::partials.breadcrumbs", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fpartials%2Fbreadcrumbs.blade.php&line=1", "ajax": false, "filename": "breadcrumbs.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::partials.breadcrumbs"}, {"name": "1x shop::components.shimmer.products.view", "param_count": null, "params": [], "start": **********.362828, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/shimmer/products/view.blade.phpshop::components.shimmer.products.view", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fproducts%2Fview.blade.php&line=1", "ajax": false, "filename": "view.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.shimmer.products.view"}, {"name": "3x shop::components.shimmer.products.gallery", "param_count": null, "params": [], "start": **********.363882, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/shimmer/products/gallery.blade.phpshop::components.shimmer.products.gallery", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fproducts%2Fgallery.blade.php&line=1", "ajax": false, "filename": "gallery.blade.php", "line": "?"}, "render_count": 3, "name_original": "shop::components.shimmer.products.gallery"}, {"name": "2x shop::components.tabs.item", "param_count": null, "params": [], "start": **********.365171, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/tabs/item.blade.phpshop::components.tabs.item", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Ftabs%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::components.tabs.item"}, {"name": "2x shop::products.view.reviews", "param_count": null, "params": [], "start": **********.366413, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/products/view/reviews.blade.phpshop::products.view.reviews", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview%2Freviews.blade.php&line=1", "ajax": false, "filename": "reviews.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::products.view.reviews"}, {"name": "3x shop::components.shimmer.products.reviews.index", "param_count": null, "params": [], "start": **********.368639, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/shimmer/products/reviews/index.blade.phpshop::components.shimmer.products.reviews.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fproducts%2Freviews%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "shop::components.shimmer.products.reviews.index"}, {"name": "3x shop::components.shimmer.products.reviews.card", "param_count": null, "params": [], "start": **********.369624, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/shimmer/products/reviews/card.blade.phpshop::components.shimmer.products.reviews.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fproducts%2Freviews%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}, "render_count": 3, "name_original": "shop::components.shimmer.products.reviews.card"}, {"name": "6x shop::components.form.control-group.control", "param_count": null, "params": [], "start": **********.370869, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/form/control-group/control.blade.phpshop::components.form.control-group.control", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Fcontrol.blade.php&line=1", "ajax": false, "filename": "control.blade.php", "line": "?"}, "render_count": 6, "name_original": "shop::components.form.control-group.control"}, {"name": "1x shop::components.media.index", "param_count": null, "params": [], "start": **********.371892, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/media/index.blade.phpshop::components.media.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fmedia%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.media.index"}, {"name": "3x shop::components.media.images.lazy", "param_count": null, "params": [], "start": **********.372871, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/media/images/lazy.blade.phpshop::components.media.images.lazy", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fmedia%2Fimages%2Flazy.blade.php&line=1", "ajax": false, "filename": "lazy.blade.php", "line": "?"}, "render_count": 3, "name_original": "shop::components.media.images.lazy"}, {"name": "5x shop::components.form.control-group.error", "param_count": null, "params": [], "start": **********.373447, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/form/control-group/error.blade.phpshop::components.form.control-group.error", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 5, "name_original": "shop::components.form.control-group.error"}, {"name": "5x shop::components.form.control-group.index", "param_count": null, "params": [], "start": **********.373893, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/form/control-group/index.blade.phpshop::components.form.control-group.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 5, "name_original": "shop::components.form.control-group.index"}, {"name": "4x shop::components.form.control-group.label", "param_count": null, "params": [], "start": **********.37461, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/form/control-group/label.blade.phpshop::components.form.control-group.label", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 4, "name_original": "shop::components.form.control-group.label"}, {"name": "4x shop::components.form.index", "param_count": null, "params": [], "start": **********.389556, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/form/index.blade.phpshop::components.form.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 4, "name_original": "shop::components.form.index"}, {"name": "2x shop::components.image-zoomer.index", "param_count": null, "params": [], "start": **********.424233, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/image-zoomer/index.blade.phpshop::components.image-zoomer.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fimage-zoomer%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::components.image-zoomer.index"}, {"name": "1x shop::components.tabs.index", "param_count": null, "params": [], "start": **********.427754, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/tabs/index.blade.phpshop::components.tabs.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Ftabs%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.tabs.index"}, {"name": "1x shop::components.shimmer.tabs.index", "param_count": null, "params": [], "start": **********.428822, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/shimmer/tabs/index.blade.phpshop::components.shimmer.tabs.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Ftabs%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.shimmer.tabs.index"}, {"name": "3x shop::components.accordion.index", "param_count": null, "params": [], "start": **********.429512, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/accordion/index.blade.phpshop::components.accordion.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Faccordion%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "shop::components.accordion.index"}, {"name": "3x shop::components.products.carousel", "param_count": null, "params": [], "start": **********.433814, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/products/carousel.blade.phpshop::components.products.carousel", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fproducts%2Fcarousel.blade.php&line=1", "ajax": false, "filename": "carousel.blade.php", "line": "?"}, "render_count": 3, "name_original": "shop::components.products.carousel"}, {"name": "4x shop::components.shimmer.products.carousel", "param_count": null, "params": [], "start": **********.434283, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/shimmer/products/carousel.blade.phpshop::components.shimmer.products.carousel", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fproducts%2Fcarousel.blade.php&line=1", "ajax": false, "filename": "carousel.blade.php", "line": "?"}, "render_count": 4, "name_original": "shop::components.shimmer.products.carousel"}, {"name": "4x shop::components.shimmer.products.cards.grid", "param_count": null, "params": [], "start": **********.434673, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/shimmer/products/cards/grid.blade.phpshop::components.shimmer.products.cards.grid", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fproducts%2Fcards%2Fgrid.blade.php&line=1", "ajax": false, "filename": "grid.blade.php", "line": "?"}, "render_count": 4, "name_original": "shop::components.shimmer.products.cards.grid"}, {"name": "1x shop::components.products.card", "param_count": null, "params": [], "start": **********.43509, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/products/card.blade.phpshop::components.products.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fproducts%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.products.card"}, {"name": "2x shop::components.products.ratings", "param_count": null, "params": [], "start": **********.438139, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/products/ratings.blade.phpshop::components.products.ratings", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fproducts%2Fratings.blade.php&line=1", "ajax": false, "filename": "ratings.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::components.products.ratings"}, {"name": "3x shop::components.button.index", "param_count": null, "params": [], "start": **********.466793, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/button/index.blade.phpshop::components.button.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "shop::components.button.index"}, {"name": "1x marketplace::shop.products.top-selling", "param_count": null, "params": [], "start": **********.469175, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src/resources/views/shop/products/top-selling.blade.phpmarketplace::shop.products.top-selling", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FResources%2Fviews%2Fshop%2Fproducts%2Ftop-selling.blade.php&line=1", "ajax": false, "filename": "top-selling.blade.php", "line": "?"}, "render_count": 1, "name_original": "marketplace::shop.products.top-selling"}, {"name": "1x shop::products.view.gallery", "param_count": null, "params": [], "start": **********.477626, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/products/view/gallery.blade.phpshop::products.view.gallery", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview%2Fgallery.blade.php&line=1", "ajax": false, "filename": "gallery.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::products.view.gallery"}, {"name": "1x shop::products.view.gallery.desktop", "param_count": null, "params": [], "start": **********.478883, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/products/view/gallery/desktop.blade.phpshop::products.view.gallery.desktop", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview%2Fgallery%2Fdesktop.blade.php&line=1", "ajax": false, "filename": "desktop.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::products.view.gallery.desktop"}, {"name": "1x shop::products.view.gallery.mobile", "param_count": null, "params": [], "start": **********.479716, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/products/view/gallery/mobile.blade.phpshop::products.view.gallery.mobile", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview%2Fgallery%2Fmobile.blade.php&line=1", "ajax": false, "filename": "mobile.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::products.view.gallery.mobile"}, {"name": "1x shop::products.prices.index", "param_count": null, "params": [], "start": **********.5012, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/products/prices/index.blade.phpshop::products.prices.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::products.prices.index"}, {"name": "1x shop::products.view.types.simple", "param_count": null, "params": [], "start": **********.515441, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/products/view/types/simple.blade.phpshop::products.view.types.simple", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview%2Ftypes%2Fsimple.blade.php&line=1", "ajax": false, "filename": "simple.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::products.view.types.simple"}, {"name": "1x shop::products.view.customizable-options", "param_count": null, "params": [], "start": **********.516063, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/products/view/customizable-options.blade.phpshop::products.view.customizable-options", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview%2Fcustomizable-options.blade.php&line=1", "ajax": false, "filename": "customizable-options.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::products.view.customizable-options"}, {"name": "1x shop::products.view.types.configurable", "param_count": null, "params": [], "start": **********.531857, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/products/view/types/configurable.blade.phpshop::products.view.types.configurable", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview%2Ftypes%2Fconfigurable.blade.php&line=1", "ajax": false, "filename": "configurable.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::products.view.types.configurable"}, {"name": "1x shop::products.view.types.grouped", "param_count": null, "params": [], "start": **********.533906, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/products/view/types/grouped.blade.phpshop::products.view.types.grouped", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview%2Ftypes%2Fgrouped.blade.php&line=1", "ajax": false, "filename": "grouped.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::products.view.types.grouped"}, {"name": "1x shop::products.view.types.bundle", "param_count": null, "params": [], "start": **********.534869, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/products/view/types/bundle.blade.phpshop::products.view.types.bundle", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview%2Ftypes%2Fbundle.blade.php&line=1", "ajax": false, "filename": "bundle.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::products.view.types.bundle"}, {"name": "1x shop::products.view.types.downloadable", "param_count": null, "params": [], "start": **********.535966, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/products/view/types/downloadable.blade.phpshop::products.view.types.downloadable", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview%2Ftypes%2Fdownloadable.blade.php&line=1", "ajax": false, "filename": "downloadable.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::products.view.types.downloadable"}, {"name": "1x shop::products.view.types.booking", "param_count": null, "params": [], "start": **********.536843, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/products/view/types/booking.blade.phpshop::products.view.types.booking", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview%2Ftypes%2Fbooking.blade.php&line=1", "ajax": false, "filename": "booking.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::products.view.types.booking"}, {"name": "2x shop::components.quantity-changer.index", "param_count": null, "params": [], "start": **********.538026, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/quantity-changer/index.blade.phpshop::components.quantity-changer.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fquantity-changer%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::components.quantity-changer.index"}, {"name": "1x social_share::share", "param_count": null, "params": [], "start": **********.569693, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\SocialShare\\src\\Providers/../Resources/views/share.blade.phpsocial_share::share", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FSocialShare%2Fsrc%2FResources%2Fviews%2Fshare.blade.php&line=1", "ajax": false, "filename": "share.blade.php", "line": "?"}, "render_count": 1, "name_original": "social_share::share"}, {"name": "1x marketplace::shop.products.product-sellers", "param_count": null, "params": [], "start": **********.576289, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src/resources/views/shop/products/product-sellers.blade.phpmarketplace::shop.products.product-sellers", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FResources%2Fviews%2Fshop%2Fproducts%2Fproduct-sellers.blade.php&line=1", "ajax": false, "filename": "product-sellers.blade.php", "line": "?"}, "render_count": 1, "name_original": "marketplace::shop.products.product-sellers"}, {"name": "1x marketplace::components.shop.shimmer.ratings", "param_count": null, "params": [], "start": **********.590249, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src/resources/views/components/shop/shimmer/ratings.blade.phpmarketplace::components.shop.shimmer.ratings", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshop%2Fshimmer%2Fratings.blade.php&line=1", "ajax": false, "filename": "ratings.blade.php", "line": "?"}, "render_count": 1, "name_original": "marketplace::components.shop.shimmer.ratings"}, {"name": "1x marketplace::components.shop.products.star-rating", "param_count": null, "params": [], "start": **********.591018, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src/resources/views/components/shop/products/star-rating.blade.phpmarketplace::components.shop.products.star-rating", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshop%2Fproducts%2Fstar-rating.blade.php&line=1", "ajax": false, "filename": "star-rating.blade.php", "line": "?"}, "render_count": 1, "name_original": "marketplace::components.shop.products.star-rating"}, {"name": "1x marketplace::components.seller.shimmer.ratings", "param_count": null, "params": [], "start": **********.59202, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src/resources/views/components/seller/shimmer/ratings.blade.phpmarketplace::components.seller.shimmer.ratings", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fseller%2Fshimmer%2Fratings.blade.php&line=1", "ajax": false, "filename": "ratings.blade.php", "line": "?"}, "render_count": 1, "name_original": "marketplace::components.seller.shimmer.ratings"}, {"name": "1x marketplace::shop.products.report", "param_count": null, "params": [], "start": **********.592543, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src/resources/views/shop/products/report.blade.phpmarketplace::shop.products.report", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FResources%2Fviews%2Fshop%2Fproducts%2Freport.blade.php&line=1", "ajax": false, "filename": "report.blade.php", "line": "?"}, "render_count": 1, "name_original": "marketplace::shop.products.report"}, {"name": "1x shop::components.modal.index", "param_count": null, "params": [], "start": **********.677096, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/modal/index.blade.phpshop::components.modal.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fmodal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.modal.index"}, {"name": "1x shop::components.layouts.index", "param_count": null, "params": [], "start": **********.688959, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/index.blade.phpshop::components.layouts.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.index"}, {"name": "1x marketplace::components.shop.layouts.header.style", "param_count": null, "params": [], "start": **********.695145, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src/resources/views/components/shop/layouts/header/style.blade.phpmarketplace::components.shop.layouts.header.style", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshop%2Flayouts%2Fheader%2Fstyle.blade.php&line=1", "ajax": false, "filename": "style.blade.php", "line": "?"}, "render_count": 1, "name_original": "marketplace::components.shop.layouts.header.style"}, {"name": "1x shop::components.flash-group.index", "param_count": null, "params": [], "start": **********.735167, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/flash-group/index.blade.phpshop::components.flash-group.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fflash-group%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.flash-group.index"}, {"name": "2x shop::components.flash-group.item", "param_count": null, "params": [], "start": **********.735713, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/flash-group/item.blade.phpshop::components.flash-group.item", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fflash-group%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::components.flash-group.item"}, {"name": "1x shop::components.modal.confirm", "param_count": null, "params": [], "start": **********.736508, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/modal/confirm.blade.phpshop::components.modal.confirm", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fmodal%2Fconfirm.blade.php&line=1", "ajax": false, "filename": "confirm.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.modal.confirm"}, {"name": "1x shop::components.layouts.header.index", "param_count": null, "params": [], "start": **********.736981, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/index.blade.phpshop::components.layouts.header.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.header.index"}, {"name": "1x shop::components.layouts.header.desktop.top", "param_count": null, "params": [], "start": **********.744164, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/top.blade.phpshop::components.layouts.header.desktop.top", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Ftop.blade.php&line=1", "ajax": false, "filename": "top.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.header.desktop.top"}, {"name": "4x shop::components.dropdown.index", "param_count": null, "params": [], "start": **********.745088, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems/resources/themes/default/views/components/dropdown/index.blade.phpshop::components.dropdown.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fresources%2Fthemes%2Fdefault%2Fviews%2Fcomponents%2Fdropdown%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 4, "name_original": "shop::components.dropdown.index"}, {"name": "1x shop::components.layouts.header.desktop.index", "param_count": null, "params": [], "start": **********.768035, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/index.blade.phpshop::components.layouts.header.desktop.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.header.desktop.index"}, {"name": "1x shop::components.layouts.header.desktop.bottom", "param_count": null, "params": [], "start": **********.768533, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/bottom.blade.phpshop::components.layouts.header.desktop.bottom", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Fbottom.blade.php&line=1", "ajax": false, "filename": "bottom.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.header.desktop.bottom"}, {"name": "2x shop::search.images.index", "param_count": null, "params": [], "start": **********.790507, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/search/images/index.blade.phpshop::search.images.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fsearch%2Fimages%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::search.images.index"}, {"name": "2x shop::checkout.cart.mini-cart", "param_count": null, "params": [], "start": **********.795142, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/checkout/cart/mini-cart.blade.phpshop::checkout.cart.mini-cart", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcheckout%2Fcart%2Fmini-cart.blade.php&line=1", "ajax": false, "filename": "mini-cart.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::checkout.cart.mini-cart"}, {"name": "5x shop::components.drawer.index", "param_count": null, "params": [], "start": **********.814711, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/drawer/index.blade.phpshop::components.drawer.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fdrawer%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 5, "name_original": "shop::components.drawer.index"}, {"name": "2x marketplace::components.shop.layouts.header.sell", "param_count": null, "params": [], "start": **********.830519, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src/resources/views/components/shop/layouts/header/sell.blade.phpmarketplace::components.shop.layouts.header.sell", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshop%2Flayouts%2Fheader%2Fsell.blade.php&line=1", "ajax": false, "filename": "sell.blade.php", "line": "?"}, "render_count": 2, "name_original": "marketplace::components.shop.layouts.header.sell"}, {"name": "2x marketplace::components.shop.layouts.header.profile", "param_count": null, "params": [], "start": **********.83338, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src/resources/views/components/shop/layouts/header/profile.blade.phpmarketplace::components.shop.layouts.header.profile", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshop%2Flayouts%2Fheader%2Fprofile.blade.php&line=1", "ajax": false, "filename": "profile.blade.php", "line": "?"}, "render_count": 2, "name_original": "marketplace::components.shop.layouts.header.profile"}, {"name": "1x shop::components.layouts.header.mobile.index", "param_count": null, "params": [], "start": **********.841379, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.phpshop::components.layouts.header.mobile.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fmobile%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.header.mobile.index"}, {"name": "1x shop::components.layouts.services", "param_count": null, "params": [], "start": **********.868808, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/services.blade.phpshop::components.layouts.services", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fservices.blade.php&line=1", "ajax": false, "filename": "services.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.services"}, {"name": "1x shop::components.layouts.footer.index", "param_count": null, "params": [], "start": **********.877964, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/footer/index.blade.phpshop::components.layouts.footer.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Ffooter%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.footer.index"}, {"name": "1x core::blade.tracer.style", "param_count": null, "params": [], "start": **********.905426, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src/resources/views/blade/tracer/style.blade.phpcore::blade.tracer.style", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FCore%2Fsrc%2FResources%2Fviews%2Fblade%2Ftracer%2Fstyle.blade.php&line=1", "ajax": false, "filename": "style.blade.php", "line": "?"}, "render_count": 1, "name_original": "core::blade.tracer.style"}, {"name": "1x paypal::checkout.onepage.paypal-smart-button", "param_count": null, "params": [], "start": **********.905779, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Paypal\\src/resources/views/checkout/onepage/paypal-smart-button.blade.phppaypal::checkout.onepage.paypal-smart-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FPaypal%2Fsrc%2FResources%2Fviews%2Fcheckout%2Fonepage%2Fpaypal-smart-button.blade.php&line=1", "ajax": false, "filename": "paypal-smart-button.blade.php", "line": "?"}, "render_count": 1, "name_original": "paypal::checkout.onepage.paypal-smart-button"}]}, "queries": {"count": 100, "nb_statements": 100, "nb_visible_statements": 100, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.11999000000000001, "accumulated_duration_str": "120ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "installer_locale", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "theme", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Theme.php", "line": 32}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.060155, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mditems", "explain": null, "start_percent": 0, "width_percent": 0.625}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "currency", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "installer_locale", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 40}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.064047, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mditems", "explain": null, "start_percent": 0.625, "width_percent": 0.533}, {"sql": "select * from `currencies` where `code` = 'USD'", "type": "query", "params": [], "bindings": ["USD"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 296}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 295}], "start": **********.069062, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "mditems", "explain": null, "start_percent": 1.158, "width_percent": 0.467}, {"sql": "select * from `categories` where exists (select * from `category_translations` where `categories`.`id` = `category_translations`.`category_id` and `category_translations`.`slug` = 'california-gold-nutrition-sport-creatine-monohydrate-unflavored-1-lb-454-g') limit 1", "type": "query", "params": [], "bindings": ["california-gold-nutrition-sport-creatine-monohydrate-unflavored-1-lb-454-g"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 213}, {"index": 17, "namespace": null, "name": "packages/Webkul/Marketplace/src/Http/Controllers/Shop/ProductsCategoriesProxyController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Http\\Controllers\\Shop\\ProductsCategoriesProxyController.php", "line": 65}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.0801709, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "CategoryRepository.php:213", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 213}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FCategory%2Fsrc%2FRepositories%2FCategoryRepository.php&line=213", "ajax": false, "filename": "CategoryRepository.php", "line": "213"}, "connection": "mditems", "explain": null, "start_percent": 1.625, "width_percent": 1.033}, {"sql": "select * from `attributes` where `code` = 'url_key'", "type": "query", "params": [], "bindings": ["url_key"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 127}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 185}], "start": **********.084531, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "mditems", "explain": null, "start_percent": 2.659, "width_percent": 0.842}, {"sql": "select * from `product_attribute_values` where `attribute_id` = 3 and `text_value` = 'california-gold-nutrition-sport-creatine-monohydrate-unflavored-1-lb-454-g'", "type": "query", "params": [], "bindings": [3, "california-gold-nutrition-sport-creatine-monohydrate-unflavored-1-lb-454-g"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 129}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 185}, {"index": 19, "namespace": null, "name": "packages/Webkul/Marketplace/src/Http/Controllers/Shop/ProductsCategoriesProxyController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Http\\Controllers\\Shop\\ProductsCategoriesProxyController.php", "line": 80}], "start": **********.089448, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 3.5, "width_percent": 0.892}, {"sql": "select * from `products` where `products`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 163}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 185}, {"index": 23, "namespace": null, "name": "packages/Webkul/Marketplace/src/Http/Controllers/Shop/ProductsCategoriesProxyController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Http\\Controllers\\Shop\\ProductsCategoriesProxyController.php", "line": 80}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.0966249, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "ProductRepository.php:163", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductRepository.php&line=163", "ajax": false, "filename": "ProductRepository.php", "line": "163"}, "connection": "mditems", "explain": null, "start_percent": 4.392, "width_percent": 1.517}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 514}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Marketplace/src/Http/Controllers/Shop/ProductsCategoriesProxyController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Http\\Controllers\\Shop\\ProductsCategoriesProxyController.php", "line": 84}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.102659, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mditems", "explain": null, "start_percent": 5.909, "width_percent": 0.775}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeRepository.php", "line": 213}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 514}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Marketplace/src/Http/Controllers/Shop/ProductsCategoriesProxyController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Http\\Controllers\\Shop\\ProductsCategoriesProxyController.php", "line": 84}], "start": **********.107342, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "AttributeFamily.php:53", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=53", "ajax": false, "filename": "AttributeFamily.php", "line": "53"}, "connection": "mditems", "explain": null, "start_percent": 6.684, "width_percent": 1.333}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 2 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 24, "namespace": null, "name": "packages/Webkul/Marketplace/src/Http/Controllers/Shop/ProductsCategoriesProxyController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Http\\Controllers\\Shop\\ProductsCategoriesProxyController.php", "line": 84}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.114907, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mditems", "explain": null, "start_percent": 8.017, "width_percent": 1.133}, {"sql": "select * from `marketplace_products` where `product_id` = 2 and `is_owner` = 1", "type": "query", "params": [], "bindings": [2, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": null, "name": "packages/Webkul/Marketplace/src/Http/Controllers/Shop/ProductsCategoriesProxyController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Http\\Controllers\\Shop\\ProductsCategoriesProxyController.php", "line": 91}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.125798, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 9.151, "width_percent": 0.817}, {"sql": "select * from `marketplace_sellers` where `marketplace_sellers`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Marketplace/src/Http/Controllers/Shop/ProductsCategoriesProxyController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Http\\Controllers\\Shop\\ProductsCategoriesProxyController.php", "line": 99}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.139286, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "ProductsCategoriesProxyController.php:99", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Marketplace/src/Http/Controllers/Shop/ProductsCategoriesProxyController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Http\\Controllers\\Shop\\ProductsCategoriesProxyController.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FHttp%2FControllers%2FShop%2FProductsCategoriesProxyController.php&line=99", "ajax": false, "filename": "ProductsCategoriesProxyController.php", "line": "99"}, "connection": "mditems", "explain": null, "start_percent": 9.967, "width_percent": 1.033}, {"sql": "select * from `products` where `products`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Marketplace/src/Repositories/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Repositories\\ProductRepository.php", "line": 743}, {"index": 22, "namespace": null, "name": "packages/Webkul/Marketplace/src/Http/Controllers/Shop/ProductsCategoriesProxyController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Http\\Controllers\\Shop\\ProductsCategoriesProxyController.php", "line": 101}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.144366, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "ProductRepository.php:743", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Marketplace/src/Repositories/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Repositories\\ProductRepository.php", "line": 743}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FRepositories%2FProductRepository.php&line=743", "ajax": false, "filename": "ProductRepository.php", "line": "743"}, "connection": "mditems", "explain": null, "start_percent": 11.001, "width_percent": 0.742}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 514}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Marketplace/src/Repositories/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Repositories\\ProductRepository.php", "line": 743}, {"index": 26, "namespace": null, "name": "packages/Webkul/Marketplace/src/Http/Controllers/Shop/ProductsCategoriesProxyController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Http\\Controllers\\Shop\\ProductsCategoriesProxyController.php", "line": 101}], "start": **********.1493971, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mditems", "explain": null, "start_percent": 11.743, "width_percent": 0.692}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 2 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 24, "namespace": null, "name": "packages/Webkul/Marketplace/src/Repositories/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Repositories\\ProductRepository.php", "line": 743}, {"index": 25, "namespace": null, "name": "packages/Webkul/Marketplace/src/Http/Controllers/Shop/ProductsCategoriesProxyController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Http\\Controllers\\Shop\\ProductsCategoriesProxyController.php", "line": 101}], "start": **********.155403, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mditems", "explain": null, "start_percent": 12.434, "width_percent": 1.083}, {"sql": "select * from `visits` where (`method` = 'GET' and `url` = 'http://127.0.0.1:8000/california-gold-nutrition-sport-creatine-monohydrate-unflavored-1-lb-454-g' and `ip` = '127.0.0.1' and `visitor_id` is null and `visitor_type` is null and `channel_id` = 1) order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["GET", "http://127.0.0.1:8000/california-gold-nutrition-sport-creatine-monohydrate-unflavored-1-lb-454-g", "127.0.0.1", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Jobs/UpdateCreateVisitIndex.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Jobs\\UpdateCreateVisitIndex.php", "line": 45}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.197387, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "UpdateCreateVisitIndex.php:45", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Jobs/UpdateCreateVisitIndex.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Jobs\\UpdateCreateVisitIndex.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FCore%2Fsrc%2FJobs%2FUpdateCreateVisitIndex.php&line=45", "ajax": false, "filename": "UpdateCreateVisitIndex.php", "line": "45"}, "connection": "mditems", "explain": null, "start_percent": 13.518, "width_percent": 1.592}, {"sql": "insert into `visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `channel_id`, `marketplace_seller_id`, `visitable_id`, `visitable_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://127.0.0.1:8000/california-gold-nutrition-sport-creatine-monohydrate-unflavored-1-lb-454-g', 'http://127.0.0.1:8000/', '[]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\\\"host\\\":[\\\"127.0.0.1:8000\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"sec-ch-ua\\\":[\\\"\\\\\\\"Not;A=Brand\\\\\\\";v=\\\\\\\"99\\\\\\\", \\\\\\\"Google Chrome\\\\\\\";v=\\\\\\\"139\\\\\\\", \\\\\\\"Chromium\\\\\\\";v=\\\\\\\"139\\\\\\\"\\\"],\\\"sec-ch-ua-mobile\\\":[\\\"?0\\\"],\\\"sec-ch-ua-platform\\\":[\\\"\\\\\\\"Windows\\\\\\\"\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\\\/537.36 (KHTML, like Gecko) Chrome\\\\/********* Safari\\\\/537.36\\\"],\\\"sec-purpose\\\":[\\\"prefetch;prerender\\\"],\\\"purpose\\\":[\\\"prefetch\\\"],\\\"sec-speculation-tags\\\":[\\\"null\\\"],\\\"accept\\\":[\\\"text\\\\/html,application\\\\/xhtml+xml,application\\\\/xml;q=0.9,image\\\\/avif,image\\\\/webp,image\\\\/apng,*\\\\/*;q=0.8,application\\\\/signed-exchange;v=b3;q=0.7\\\"],\\\"sec-fetch-site\\\":[\\\"same-origin\\\"],\\\"sec-fetch-mode\\\":[\\\"navigate\\\"],\\\"sec-fetch-dest\\\":[\\\"document\\\"],\\\"referer\\\":[\\\"http:\\\\/\\\\/127.0.0.1:8000\\\\/\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate, br, zstd\\\"],\\\"accept-language\\\":[\\\"en-US,en;q=0.9\\\"],\\\"cookie\\\":[\\\"_gcl_au=1.1.1178683659.1752735529; XSRF-TOKEN=eyJpdiI6IkV0UjV3VmVjSWhjSUpOazUrQ3VhZlE9PSIsInZhbHVlIjoiM2dBdVNrTndMendUWVhlNVQ5YTB6dDRXMHpiUVQ2M2sxdUJzNjZoNlhDSGcyTnRuYnhNSXR3Z29VQ1JSeEJBVS9hdndPQytxd2dmSXRSdnE0bVNBbytiUFlrcHVvZnY0UHBkYjhzay9HRk1qSlJaWTZEV0pqdzNBd09icExkeXIiLCJtYWMiOiI4YzQyYTg3M2NkZGM2ZWI3MWZkZTI3NWIyZDM0MzA1YWViYmNkZWZkZjk1MThmMjRhZDkwNDg4ZmQ2MjU1ZDkyIiwidGFnIjoiIn0%3D; bagisto_session=eyJpdiI6InhIWFNiT3VuQThjeStDQktrY3VUNkE9PSIsInZhbHVlIjoiazBQK3pCYkFRVUY2dnlWUExDYUtJNlNYOCs5NFN2VEZTL0hyNjY0UVMxODd1dVZZSERKd1VjZE5MTi9ZdGhVM1RHTnFGVlFKMXBJMEVzd3c4L2ViZHpINzk5c0ZzRE1JYS8wZWoyV0dnS0dSd0NuZ29iRWNjQnkxZlZuVHliZnkiLCJtYWMiOiI1MDhjYjRkYmY0NGY2ODZiNWEyN2RkNWRlMTQxNWEyNTYyYzA0YjM3NmYwYTU5NGI5ODQ3NDg3MWI2NmRkYzA4IiwidGFnIjoiIn0%3D\\\"]}', '', 'Windows', 'Chrome', '127.0.0.1', null, null, 1, 4, 2, 'Webkul\\\\Product\\\\Models\\\\Product', '2025-08-19 18:20:17', '2025-08-19 18:20:17')", "type": "query", "params": [], "bindings": ["GET", "[]", "http://127.0.0.1:8000/california-gold-nutrition-sport-creatine-monohydrate-unflavored-1-lb-454-g", "http://127.0.0.1:8000/", "[]", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "{\"host\":[\"127.0.0.1:8000\"],\"connection\":[\"keep-alive\"],\"sec-ch-ua\":[\"\\\"Not;A=Brand\\\";v=\\\"99\\\", \\\"Google Chrome\\\";v=\\\"139\\\", \\\"Chromium\\\";v=\\\"139\\\"\"],\"sec-ch-ua-mobile\":[\"?0\"],\"sec-ch-ua-platform\":[\"\\\"Windows\\\"\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"sec-purpose\":[\"prefetch;prerender\"],\"purpose\":[\"prefetch\"],\"sec-speculation-tags\":[\"null\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"sec-fetch-site\":[\"same-origin\"],\"sec-fetch-mode\":[\"navigate\"],\"sec-fetch-dest\":[\"document\"],\"referer\":[\"http:\\/\\/127.0.0.1:8000\\/\"],\"accept-encoding\":[\"gzip, deflate, br, zstd\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"cookie\":[\"_gcl_au=1.1.1178683659.1752735529; XSRF-TOKEN=eyJpdiI6IkV0UjV3VmVjSWhjSUpOazUrQ3VhZlE9PSIsInZhbHVlIjoiM2dBdVNrTndMendUWVhlNVQ5YTB6dDRXMHpiUVQ2M2sxdUJzNjZoNlhDSGcyTnRuYnhNSXR3Z29VQ1JSeEJBVS9hdndPQytxd2dmSXRSdnE0bVNBbytiUFlrcHVvZnY0UHBkYjhzay9HRk1qSlJaWTZEV0pqdzNBd09icExkeXIiLCJtYWMiOiI4YzQyYTg3M2NkZGM2ZWI3MWZkZTI3NWIyZDM0MzA1YWViYmNkZWZkZjk1MThmMjRhZDkwNDg4ZmQ2MjU1ZDkyIiwidGFnIjoiIn0%3D; bagisto_session=eyJpdiI6InhIWFNiT3VuQThjeStDQktrY3VUNkE9PSIsInZhbHVlIjoiazBQK3pCYkFRVUY2dnlWUExDYUtJNlNYOCs5NFN2VEZTL0hyNjY0UVMxODd1dVZZSERKd1VjZE5MTi9ZdGhVM1RHTnFGVlFKMXBJMEVzd3c4L2ViZHpINzk5c0ZzRE1JYS8wZWoyV0dnS0dSd0NuZ29iRWNjQnkxZlZuVHliZnkiLCJtYWMiOiI1MDhjYjRkYmY0NGY2ODZiNWEyN2RkNWRlMTQxNWEyNTYyYzA0YjM3NmYwYTU5NGI5ODQ3NDg3MWI2NmRkYzA4IiwidGFnIjoiIn0%3D\"]}", "", "Windows", "Chrome", "127.0.0.1", null, null, 1, 4, 2, "Webkul\\Product\\Models\\Product", "2025-08-19 18:20:17", "2025-08-19 18:20:17"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Jobs/UpdateCreateVisitIndex.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Jobs\\UpdateCreateVisitIndex.php", "line": 57}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.206474, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:651", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=651", "ajax": false, "filename": "BaseRepository.php", "line": "651"}, "connection": "mditems", "explain": null, "start_percent": 15.11, "width_percent": 3.384}, {"sql": "select * from `product_reviews` where `product_reviews`.`product_id` = 2 and `product_reviews`.`product_id` is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Review.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Helpers\\Review.php", "line": 28}, {"index": 22, "namespace": "view", "name": "shop::products.view", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 5}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.236863, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mditems", "explain": null, "start_percent": 18.493, "width_percent": 0.658}, {"sql": "select `rating`, count(*) as total from `product_reviews` where `product_reviews`.`product_id` = 2 and `product_reviews`.`product_id` is not null and `status` = 'approved' group by `rating` order by `rating` desc", "type": "query", "params": [], "bindings": [2, "approved"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Review.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Helpers\\Review.php", "line": 79}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Review.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Helpers\\Review.php", "line": 90}, {"index": 18, "namespace": "view", "name": "shop::products.view", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 7}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.2445529, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Review.php:79", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Review.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Helpers\\Review.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FReview.php&line=79", "ajax": false, "filename": "Review.php", "line": "79"}, "connection": "mditems", "explain": null, "start_percent": 19.152, "width_percent": 0.933}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 1 and `attributes`.`is_visible_on_front` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/View.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Helpers\\View.php", "line": 19}, {"index": 16, "namespace": "view", "name": "shop::products.view", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 9}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.2505672, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "View.php:19", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/View.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Helpers\\View.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FView.php&line=19", "ajax": false, "filename": "View.php", "line": "19"}, "connection": "mditems", "explain": null, "start_percent": 20.085, "width_percent": 1.117}, {"sql": "select * from `locales`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, {"index": 17, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 218}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 217}], "start": **********.264008, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:346", "source": {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=346", "ajax": false, "filename": "BaseRepository.php", "line": "346"}, "connection": "mditems", "explain": null, "start_percent": 21.202, "width_percent": 0.858}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` = 25 and `attribute_translations`.`attribute_id` is not null", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/View.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Helpers\\View.php", "line": 60}], "start": **********.274326, "duration": 0.00423, "duration_str": "4.23ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mditems", "explain": null, "start_percent": 22.06, "width_percent": 3.525}, {"sql": "select * from `channels` where `code` = 'default'", "type": "query", "params": [], "bindings": ["default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 296}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 295}], "start": **********.291142, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "mditems", "explain": null, "start_percent": 25.585, "width_percent": 0.583}, {"sql": "select * from `locales` where `locales`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Core.php", "line": 215}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 470}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 26, "namespace": "view", "name": "shop::products.view", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 16}], "start": **********.296982, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mditems", "explain": null, "start_percent": 26.169, "width_percent": 0.567}, {"sql": "select * from `core_config` where `code` = 'catalog.rich_snippets.products.enable'", "type": "query", "params": [], "bindings": ["catalog.rich_snippets.products.enable"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.311854, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 26.736, "width_percent": 0.925}, {"sql": "select * from `product_images` where `product_images`.`product_id` = 2 and `product_images`.`product_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/ProductImage.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\ProductImage.php", "line": 109}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/ProductImage.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\ProductImage.php", "line": 98}, {"index": 23, "namespace": "view", "name": "shop::products.view", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.3222518, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mditems", "explain": null, "start_percent": 27.661, "width_percent": 1.675}, {"sql": "select * from `core_config` where `code` = 'general.general.breadcrumbs.shop'", "type": "query", "params": [], "bindings": ["general.general.breadcrumbs.shop"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.354795, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 29.336, "width_percent": 0.55}, {"sql": "select * from `core_config` where `code` = 'catalog.products.review.guest_review'", "type": "query", "params": [], "bindings": ["catalog.products.review.guest_review"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.3784409, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 29.886, "width_percent": 0.6}, {"sql": "select * from `core_config` where `code` = 'catalog.products.review.summary'", "type": "query", "params": [], "bindings": ["catalog.products.review.summary"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.397242, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 30.486, "width_percent": 0.883}, {"sql": "select * from `core_config` where `code` = 'catalog.products.review.customer_review'", "type": "query", "params": [], "bindings": ["catalog.products.review.customer_review"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.4059372, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 31.369, "width_percent": 0.533}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.review_translation.enabled' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["general.magic_ai.review_translation.enabled", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.4204018, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 31.903, "width_percent": 0.925}, {"sql": "select * from `core_config` where `code` = 'customer.settings.wishlist.wishlist_option'", "type": "query", "params": [], "bindings": ["customer.settings.wishlist.wishlist_option"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.440172, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 32.828, "width_percent": 0.65}, {"sql": "select * from `core_config` where `code` = 'catalog.products.settings.compare_option'", "type": "query", "params": [], "bindings": ["catalog.products.settings.compare_option"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.445606, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 33.478, "width_percent": 0.567}, {"sql": "select * from `core_config` where `code` = 'sales.checkout.shopping_cart.cart_page'", "type": "query", "params": [], "bindings": ["sales.checkout.shopping_cart.cart_page"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.4503438, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 34.045, "width_percent": 0.925}, {"sql": "select * from `marketplace_products` where `product_id` = 2 and `is_owner` = 1", "type": "query", "params": [], "bindings": [2, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": "view", "name": "marketplace::shop.products.top-selling", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src/resources/views/shop/products/top-selling.blade.php", "line": 4}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.470583, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 34.97, "width_percent": 0.725}, {"sql": "select * from `marketplace_sellers` where `marketplace_sellers`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 23, "namespace": "view", "name": "marketplace::shop.products.top-selling", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src/resources/views/shop/products/top-selling.blade.php", "line": 4}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.473136, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 35.695, "width_percent": 0.508}, {"sql": "select * from `product_videos` where `product_videos`.`product_id` = 2 and `product_videos`.`product_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/ProductVideo.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\ProductVideo.php", "line": 23}, {"index": 22, "namespace": "view", "name": "shop::products.view.gallery", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/products/view/gallery.blade.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.4817011, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mditems", "explain": null, "start_percent": 36.203, "width_percent": 0.808}, {"sql": "select * from `currency_exchange_rates` where `target_currency` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Core.php", "line": 457}, {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Core.php", "line": 479}], "start": **********.4927478, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 37.011, "width_percent": 0.8}, {"sql": "select * from `customer_groups` where `code` = 'guest'", "type": "query", "params": [], "bindings": ["guest"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Core.php", "line": 746}, {"index": 19, "namespace": null, "name": "packages/Webkul/Customer/src/Repositories/CustomerRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Customer\\src\\Repositories\\CustomerRepository.php", "line": 41}], "start": **********.4952922, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "mditems", "explain": null, "start_percent": 37.811, "width_percent": 1.558}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` = 2 and `product_price_indices`.`product_id` is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 682}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 598}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 734}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 749}], "start": **********.4986532, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mditems", "explain": null, "start_percent": 39.37, "width_percent": 0.808}, {"sql": "select * from `core_config` where `code` = 'sales.taxes.calculation.product_prices'", "type": "query", "params": [], "bindings": ["sales.taxes.calculation.product_prices"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.504647, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 40.178, "width_percent": 0.775}, {"sql": "select * from `product_customer_group_prices` where `product_customer_group_prices`.`product_id` = 2 and `product_customer_group_prices`.`product_id` is not null and (`customer_group_id` = 1 or `customer_group_id` is null) and `qty` > 1 group by `qty` order by `qty` asc", "type": "query", "params": [], "bindings": [2, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 984}, {"index": 17, "namespace": "view", "name": "shop::products.view", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/products/view.blade.php", "line": 571}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.508404, "duration": 0.00434, "duration_str": "4.34ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:984", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 984}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=984", "ajax": false, "filename": "AbstractType.php", "line": "984"}, "connection": "mditems", "explain": null, "start_percent": 40.953, "width_percent": 3.617}, {"sql": "select * from `product_grouped_products` where `associated_product_id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 115}, {"index": 22, "namespace": "view", "name": "shop::products.view.customizable-options", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/products/view/customizable-options.blade.php", "line": 1}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.517436, "duration": 0.00304, "duration_str": "3.04ms", "memory": 0, "memory_str": null, "filename": "Simple.php:115", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 115}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FSimple.php&line=115", "ajax": false, "filename": "Simple.php", "line": "115"}, "connection": "mditems", "explain": null, "start_percent": 44.57, "width_percent": 2.534}, {"sql": "select * from `product_bundle_option_products` where `product_id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 124}, {"index": 22, "namespace": "view", "name": "shop::products.view.customizable-options", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/products/view/customizable-options.blade.php", "line": 1}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.5222218, "duration": 0.0034100000000000003, "duration_str": "3.41ms", "memory": 0, "memory_str": null, "filename": "Simple.php:124", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FSimple.php&line=124", "ajax": false, "filename": "Simple.php", "line": "124"}, "connection": "mditems", "explain": null, "start_percent": 47.104, "width_percent": 2.842}, {"sql": "select * from `product_customizable_options` where `product_customizable_options`.`product_id` = 2 and `product_customizable_options`.`product_id` is not null order by `sort_order` asc", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "view", "name": "shop::products.view.customizable-options", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/products/view/customizable-options.blade.php", "line": 6}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.5271618, "duration": 0.0030299999999999997, "duration_str": "3.03ms", "memory": 0, "memory_str": null, "filename": "shop::products.view.customizable-options:6", "source": {"index": 16, "namespace": "view", "name": "shop::products.view.customizable-options", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/products/view/customizable-options.blade.php", "line": 6}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fview%2Fcustomizable-options.blade.php&line=6", "ajax": false, "filename": "customizable-options.blade.php", "line": "6"}, "connection": "mditems", "explain": null, "start_percent": 49.946, "width_percent": 2.525}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` = 2 and `product_inventory_indices`.`product_id` is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Marketplace/src/Types/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Types\\Simple.php", "line": 19}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 505}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 157}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 145}], "start": **********.542581, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mditems", "explain": null, "start_percent": 52.471, "width_percent": 0.75}, {"sql": "select * from `marketplace_products` where `product_id` = 2 and `is_owner` = 1", "type": "query", "params": [], "bindings": [2, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": null, "name": "packages/Webkul/Marketplace/src/Types/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Types\\Simple.php", "line": 26}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 505}], "start": **********.548322, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 53.221, "width_percent": 0.917}, {"sql": "select * from `marketplace_sellers` where `marketplace_sellers`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Marketplace/src/Types/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Types\\Simple.php", "line": 33}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 505}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 157}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 145}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 305}], "start": **********.550862, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Simple.php:33", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Marketplace/src/Types/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Types\\Simple.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FTypes%2FSimple.php&line=33", "ajax": false, "filename": "Simple.php", "line": "33"}, "connection": "mditems", "explain": null, "start_percent": 54.138, "width_percent": 0.875}, {"sql": "select * from `marketplace_products` where `product_id` = 2 and `is_owner` = 1", "type": "query", "params": [], "bindings": [2, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": null, "name": "packages/Webkul/Marketplace/src/Types/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Types\\Simple.php", "line": 26}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 505}], "start": **********.554287, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 55.013, "width_percent": 0.567}, {"sql": "select * from `marketplace_sellers` where `marketplace_sellers`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Marketplace/src/Types/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Types\\Simple.php", "line": 33}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 505}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 157}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 145}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 305}], "start": **********.556547, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Simple.php:33", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Marketplace/src/Types/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Types\\Simple.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FTypes%2FSimple.php&line=33", "ajax": false, "filename": "Simple.php", "line": "33"}, "connection": "mditems", "explain": null, "start_percent": 55.58, "width_percent": 0.608}, {"sql": "select * from `core_config` where `code` = 'catalog.products.storefront.buy_now_button_display'", "type": "query", "params": [], "bindings": ["catalog.products.storefront.buy_now_button_display"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.5633159, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 56.188, "width_percent": 0.933}, {"sql": "select * from `core_config` where `code` = 'catalog.products.social_share.enabled'", "type": "query", "params": [], "bindings": ["catalog.products.social_share.enabled"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.572331, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 57.121, "width_percent": 0.983}, {"sql": "select * from `marketplace_products` where `product_id` = 2 and `is_owner` = 1", "type": "query", "params": [], "bindings": [2, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": "view", "name": "marketplace::shop.products.product-sellers", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src/resources/views/shop/products/product-sellers.blade.php", "line": 8}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.577828, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 58.105, "width_percent": 0.567}, {"sql": "select * from `marketplace_sellers` where `marketplace_sellers`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 23, "namespace": "view", "name": "marketplace::shop.products.product-sellers", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src/resources/views/shop/products/product-sellers.blade.php", "line": 8}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.579934, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 58.672, "width_percent": 0.883}, {"sql": "select avg(`rating`) as aggregate from `marketplace_seller_reviews` where `marketplace_seller_reviews`.`marketplace_seller_id` = 4 and `marketplace_seller_reviews`.`marketplace_seller_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": [4, "approved"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Marketplace/src/Repositories/ReviewRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Repositories\\ReviewRepository.php", "line": 52}, {"index": 21, "namespace": "view", "name": "marketplace::shop.products.product-sellers", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src/resources/views/shop/products/product-sellers.blade.php", "line": 14}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.582505, "duration": 0.00331, "duration_str": "3.31ms", "memory": 0, "memory_str": null, "filename": "ReviewRepository.php:52", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Marketplace/src/Repositories/ReviewRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Repositories\\ReviewRepository.php", "line": 52}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FRepositories%2FReviewRepository.php&line=52", "ajax": false, "filename": "ReviewRepository.php", "line": "52"}, "connection": "mditems", "explain": null, "start_percent": 59.555, "width_percent": 2.759}, {"sql": "select count(*) as aggregate from `marketplace_products` inner join `marketplace_sellers` on `marketplace_sellers`.`id` = `marketplace_products`.`marketplace_seller_id` where `marketplace_products`.`product_id` = 2 and `marketplace_sellers`.`is_suspended` = 0 and `marketplace_sellers`.`is_approved` = 1 and `marketplace_products`.`is_owner` = 0 and `marketplace_products`.`is_approved` = 1", "type": "query", "params": [], "bindings": [2, 0, 1, 0, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 173}, {"index": 17, "namespace": null, "name": "packages/Webkul/Marketplace/src/Repositories/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Repositories\\ProductRepository.php", "line": 678}, {"index": 18, "namespace": "view", "name": "marketplace::shop.products.product-sellers", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src/resources/views/shop/products/product-sellers.blade.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.587426, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Repository.php:173", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=173", "ajax": false, "filename": "Repository.php", "line": "173"}, "connection": "mditems", "explain": null, "start_percent": 62.314, "width_percent": 0.908}, {"sql": "select * from `mp_flag_reasons` where (`status` = 1 and `type` = 'product')", "type": "query", "params": [], "bindings": [1, "product"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "marketplace::shop.products.report", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src/resources/views/shop/products/report.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.594114, "duration": 0.009689999999999999, "duration_str": "9.69ms", "memory": 0, "memory_str": null, "filename": "marketplace::shop.products.report:4", "source": {"index": 15, "namespace": "view", "name": "marketplace::shop.products.report", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src/resources/views/shop/products/report.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FResources%2Fviews%2Fshop%2Fproducts%2Freport.blade.php&line=4", "ajax": false, "filename": "report.blade.php", "line": "4"}, "connection": "mditems", "explain": null, "start_percent": 63.222, "width_percent": 8.076}, {"sql": "select * from `core_config` where `code` = 'marketplace.settings.product.flag_enabled' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["marketplace.settings.product.flag_enabled", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.60729, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 71.298, "width_percent": 0.533}, {"sql": "select * from `mp_flag_reason_translations` where `mp_flag_reason_translations`.`mp_flag_reason_id` = 1 and `mp_flag_reason_translations`.`mp_flag_reason_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": "view", "name": "marketplace::shop.products.report", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src/resources/views/shop/products/report.blade.php", "line": 114}], "start": **********.6139362, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mditems", "explain": null, "start_percent": 71.831, "width_percent": 1.392}, {"sql": "select * from `mp_flag_reason_translations` where `mp_flag_reason_translations`.`mp_flag_reason_id` = 2 and `mp_flag_reason_translations`.`mp_flag_reason_id` is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": "view", "name": "marketplace::shop.products.report", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src/resources/views/shop/products/report.blade.php", "line": 114}], "start": **********.6260338, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mditems", "explain": null, "start_percent": 73.223, "width_percent": 0.5}, {"sql": "select * from `mp_flag_reason_translations` where `mp_flag_reason_translations`.`mp_flag_reason_id` = 3 and `mp_flag_reason_translations`.`mp_flag_reason_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": "view", "name": "marketplace::shop.products.report", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src/resources/views/shop/products/report.blade.php", "line": 114}], "start": **********.635537, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mditems", "explain": null, "start_percent": 73.723, "width_percent": 0.908}, {"sql": "select * from `mp_flag_reason_translations` where `mp_flag_reason_translations`.`mp_flag_reason_id` = 4 and `mp_flag_reason_translations`.`mp_flag_reason_id` is not null", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": "view", "name": "marketplace::shop.products.report", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src/resources/views/shop/products/report.blade.php", "line": 114}], "start": **********.64635, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mditems", "explain": null, "start_percent": 74.631, "width_percent": 0.508}, {"sql": "select * from `mp_flag_reason_translations` where `mp_flag_reason_translations`.`mp_flag_reason_id` = 5 and `mp_flag_reason_translations`.`mp_flag_reason_id` is not null", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": "view", "name": "marketplace::shop.products.report", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src/resources/views/shop/products/report.blade.php", "line": 114}], "start": **********.65679, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mditems", "explain": null, "start_percent": 75.14, "width_percent": 0.592}, {"sql": "select * from `mp_flag_reason_translations` where `mp_flag_reason_translations`.`mp_flag_reason_id` = 6 and `mp_flag_reason_translations`.`mp_flag_reason_id` is not null", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": "view", "name": "marketplace::shop.products.report", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src/resources/views/shop/products/report.blade.php", "line": 114}], "start": **********.666933, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mditems", "explain": null, "start_percent": 75.731, "width_percent": 0.692}, {"sql": "select * from `locales` where `code` = 'en'", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 296}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 295}], "start": **********.691183, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "mditems", "explain": null, "start_percent": 76.423, "width_percent": 0.725}, {"sql": "select * from `core_config` where `code` = 'general.content.custom_scripts.custom_css' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["general.content.custom_scripts.custom_css", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.698308, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 77.148, "width_percent": 1.242}, {"sql": "select * from `core_config` where `code` = 'general.content.speculation_rules.enabled'", "type": "query", "params": [], "bindings": ["general.content.speculation_rules.enabled"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.704653, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 78.39, "width_percent": 0.708}, {"sql": "select * from `core_config` where `code` = 'general.content.speculation_rules.prerender_enabled'", "type": "query", "params": [], "bindings": ["general.content.speculation_rules.prerender_enabled"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.710217, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 79.098, "width_percent": 0.925}, {"sql": "select * from `core_config` where `code` = 'general.content.speculation_rules.prerender_eagerness'", "type": "query", "params": [], "bindings": ["general.content.speculation_rules.prerender_eagerness"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.715828, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 80.023, "width_percent": 0.558}, {"sql": "select * from `core_config` where `code` = 'general.content.speculation_rules.prerender_ignore_urls'", "type": "query", "params": [], "bindings": ["general.content.speculation_rules.prerender_ignore_urls"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.7211301, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 80.582, "width_percent": 0.558}, {"sql": "select * from `core_config` where `code` = 'general.content.speculation_rules.prerender_ignore_url_params'", "type": "query", "params": [], "bindings": ["general.content.speculation_rules.prerender_ignore_url_params"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.726259, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 81.14, "width_percent": 0.542}, {"sql": "select * from `core_config` where `code` = 'general.content.speculation_rules.prefetch_enabled'", "type": "query", "params": [], "bindings": ["general.content.speculation_rules.prefetch_enabled"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.731576, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 81.682, "width_percent": 0.75}, {"sql": "select count(*) as aggregate from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "shop::components.layouts.header.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/index.blade.php", "line": 4}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.737557, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.index:4", "source": {"index": 19, "namespace": "view", "name": "shop::components.layouts.header.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/index.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Findex.blade.php&line=4", "ajax": false, "filename": "index.blade.php", "line": "4"}, "connection": "mditems", "explain": null, "start_percent": 82.432, "width_percent": 1.183}, {"sql": "select count(*) as aggregate from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "shop::components.layouts.header.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/index.blade.php", "line": 4}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.74042, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.index:4", "source": {"index": 19, "namespace": "view", "name": "shop::components.layouts.header.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/index.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Findex.blade.php&line=4", "ajax": false, "filename": "index.blade.php", "line": "4"}, "connection": "mditems", "explain": null, "start_percent": 83.615, "width_percent": 1.7}, {"sql": "select * from `core_config` where `code` = 'general.content.header_offer.title'", "type": "query", "params": [], "bindings": ["general.content.header_offer.title"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.747896, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 85.315, "width_percent": 0.642}, {"sql": "select * from `core_config` where `code` = 'general.content.header_offer.redirection_link'", "type": "query", "params": [], "bindings": ["general.content.header_offer.redirection_link"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.753643, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 85.957, "width_percent": 0.567}, {"sql": "select * from `core_config` where `code` = 'general.content.header_offer.redirection_title'", "type": "query", "params": [], "bindings": ["general.content.header_offer.redirection_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.758414, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 86.524, "width_percent": 0.542}, {"sql": "select `name` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 and `code` = 'en' order by `name` asc limit 1", "type": "query", "params": [], "bindings": [1, "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "shop::components.layouts.header.desktop.top", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/top.blade.php", "line": 162}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.762085, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.desktop.top:162", "source": {"index": 20, "namespace": "view", "name": "shop::components.layouts.header.desktop.top", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/top.blade.php", "line": 162}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Ftop.blade.php&line=162", "ajax": false, "filename": "top.blade.php", "line": "162"}, "connection": "mditems", "explain": null, "start_percent": 87.066, "width_percent": 0.467}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 order by `name` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "shop::components.layouts.header.desktop.top", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/top.blade.php", "line": 270}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.7650669, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.desktop.top:270", "source": {"index": 15, "namespace": "view", "name": "shop::components.layouts.header.desktop.top", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/top.blade.php", "line": 270}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Ftop.blade.php&line=270", "ajax": false, "filename": "top.blade.php", "line": "270"}, "connection": "mditems", "explain": null, "start_percent": 87.532, "width_percent": 0.908}, {"sql": "select * from `attributes` where `attributes`.`id` = 33 limit 1", "type": "query", "params": [], "bindings": [33], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "shop::components.layouts.header.desktop.bottom", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/bottom.blade.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.769453, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.desktop.bottom:44", "source": {"index": 20, "namespace": "view", "name": "shop::components.layouts.header.desktop.bottom", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/bottom.blade.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Fbottom.blade.php&line=44", "ajax": false, "filename": "bottom.blade.php", "line": "44"}, "connection": "mditems", "explain": null, "start_percent": 88.441, "width_percent": 0.808}, {"sql": "select * from `attribute_options` where `attribute_id` = 33", "type": "query", "params": [], "bindings": [33], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "shop::components.layouts.header.desktop.bottom", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/bottom.blade.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.771695, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.desktop.bottom:47", "source": {"index": 15, "namespace": "view", "name": "shop::components.layouts.header.desktop.bottom", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/bottom.blade.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Fbottom.blade.php&line=47", "ajax": false, "filename": "bottom.blade.php", "line": "47"}, "connection": "mditems", "explain": null, "start_percent": 89.249, "width_percent": 0.733}, {"sql": "select * from `core_config` where `code` = 'catalog.products.search.min_query_length'", "type": "query", "params": [], "bindings": ["catalog.products.search.min_query_length"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.776536, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 89.982, "width_percent": 0.5}, {"sql": "select * from `core_config` where `code` = 'catalog.products.search.max_query_length'", "type": "query", "params": [], "bindings": ["catalog.products.search.max_query_length"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.781779, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 90.483, "width_percent": 0.675}, {"sql": "select * from `core_config` where `code` = 'catalog.products.settings.image_search'", "type": "query", "params": [], "bindings": ["catalog.products.settings.image_search"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.787172, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 91.158, "width_percent": 0.592}, {"sql": "select * from `core_config` where `code` = 'sales.checkout.mini_cart.display_mini_cart'", "type": "query", "params": [], "bindings": ["sales.checkout.mini_cart.display_mini_cart"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.797682, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 91.749, "width_percent": 0.733}, {"sql": "select * from `core_config` where `code` = 'sales.checkout.my_cart.summary'", "type": "query", "params": [], "bindings": ["sales.checkout.my_cart.summary"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.803087, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 92.483, "width_percent": 0.517}, {"sql": "select * from `core_config` where `code` = 'sales.checkout.mini_cart.offer_info'", "type": "query", "params": [], "bindings": ["sales.checkout.mini_cart.offer_info"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.808883, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 92.999, "width_percent": 0.575}, {"sql": "select * from `core_config` where `code` = 'sales.taxes.shopping_cart.display_prices'", "type": "query", "params": [], "bindings": ["sales.taxes.shopping_cart.display_prices"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.8185182, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 93.574, "width_percent": 0.542}, {"sql": "select * from `core_config` where `code` = 'sales.taxes.shopping_cart.display_subtotal'", "type": "query", "params": [], "bindings": ["sales.taxes.shopping_cart.display_subtotal"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.826467, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 94.116, "width_percent": 0.575}, {"sql": "select * from `core_config` where `code` = 'general.design.categories.category_view'", "type": "query", "params": [], "bindings": ["general.design.categories.category_view"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.837313, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 94.691, "width_percent": 0.45}, {"sql": "select count(*) as aggregate from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "shop::components.layouts.header.mobile.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.php", "line": 359}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.855769, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.mobile.index:359", "source": {"index": 19, "namespace": "view", "name": "shop::components.layouts.header.mobile.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.php", "line": 359}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fmobile%2Findex.blade.php&line=359", "ajax": false, "filename": "index.blade.php", "line": "359"}, "connection": "mditems", "explain": null, "start_percent": 95.141, "width_percent": 0.567}, {"sql": "select count(*) as aggregate from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "shop::components.layouts.header.mobile.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.php", "line": 359}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.8579838, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.mobile.index:359", "source": {"index": 19, "namespace": "view", "name": "shop::components.layouts.header.mobile.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.php", "line": 359}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fmobile%2Findex.blade.php&line=359", "ajax": false, "filename": "index.blade.php", "line": "359"}, "connection": "mditems", "explain": null, "start_percent": 95.708, "width_percent": 0.35}, {"sql": "select `name` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 and `code` = 'en' order by `name` asc limit 1", "type": "query", "params": [], "bindings": [1, "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "shop::components.layouts.header.mobile.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.php", "line": 442}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.86051, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.mobile.index:442", "source": {"index": 20, "namespace": "view", "name": "shop::components.layouts.header.mobile.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.php", "line": 442}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fmobile%2Findex.blade.php&line=442", "ajax": false, "filename": "index.blade.php", "line": "442"}, "connection": "mditems", "explain": null, "start_percent": 96.058, "width_percent": 0.425}, {"sql": "select * from `core_config` where `code` = 'general.gdpr.settings.enabled' and `channel_code` = 'default' and `locale_code` = 'en'", "type": "query", "params": [], "bindings": ["general.gdpr.settings.enabled", "default", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.8654919, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 96.483, "width_percent": 0.458}, {"sql": "select * from `theme_customizations` where `type` = 'services_content' and `status` = 1 and `theme_code` = 'default' and `channel_id` = 1", "type": "query", "params": [], "bindings": ["services_content", 1, "default", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": "view", "name": "shop::components.layouts.services", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/services.blade.php", "line": 13}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.869461, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 96.941, "width_percent": 0.417}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (12)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 23, "namespace": "view", "name": "shop::components.layouts.services", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/services.blade.php", "line": 13}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.871808, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 97.358, "width_percent": 0.433}, {"sql": "select * from `theme_customizations` where `type` = 'footer_links' and `status` = 1 and `theme_code` = 'default' and `channel_id` = 1", "type": "query", "params": [], "bindings": ["footer_links", 1, "default", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": "view", "name": "shop::components.layouts.footer.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/footer/index.blade.php", "line": 17}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.878722, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 97.791, "width_percent": 0.483}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 23, "namespace": "view", "name": "shop::components.layouts.footer.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/footer/index.blade.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.880973, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 98.275, "width_percent": 0.525}, {"sql": "select * from `core_config` where `code` = 'customer.settings.newsletter.subscription'", "type": "query", "params": [], "bindings": ["customer.settings.newsletter.subscription"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.900563, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 98.8, "width_percent": 0.483}, {"sql": "select * from `core_config` where `code` = 'general.content.custom_scripts.custom_javascript' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["general.content.custom_scripts.custom_javascript", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.908245, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 99.283, "width_percent": 0.717}]}, "models": {"data": {"Webkul\\Product\\Models\\ProductAttributeValue": {"value": 57, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductAttributeValue.php&line=1", "ajax": false, "filename": "ProductAttributeValue.php", "line": "?"}}, "Webkul\\Attribute\\Models\\Attribute": {"value": 31, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "Webkul\\Core\\Models\\Locale": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Marketplace\\Models\\MpFlagReason": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FModels%2FMpFlagReason.php&line=1", "ajax": false, "filename": "MpFlagReason.php", "line": "?"}}, "Webkul\\Marketplace\\Models\\MpFlagReasonTranslation": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FModels%2FMpFlagReasonTranslation.php&line=1", "ajax": false, "filename": "MpFlagReasonTranslation.php", "line": "?"}}, "Webkul\\Marketplace\\Models\\Product": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Webkul\\Marketplace\\Models\\Seller": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FModels%2FSeller.php&line=1", "ajax": false, "filename": "Seller.php", "line": "?"}}, "Webkul\\Core\\Models\\Currency": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductPriceIndex": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductPriceIndex.php&line=1", "ajax": false, "filename": "ProductPriceIndex.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeOption": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeOption.php&line=1", "ajax": false, "filename": "AttributeOption.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeFamily": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=1", "ajax": false, "filename": "AttributeFamily.php", "line": "?"}}, "Webkul\\Core\\Models\\CoreConfig": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCoreConfig.php&line=1", "ajax": false, "filename": "CoreConfig.php", "line": "?"}}, "Webkul\\Theme\\Models\\ThemeCustomization": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FTheme%2Fsrc%2FModels%2FThemeCustomization.php&line=1", "ajax": false, "filename": "ThemeCustomization.php", "line": "?"}}, "Webkul\\Theme\\Models\\ThemeCustomizationTranslation": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FTheme%2Fsrc%2FModels%2FThemeCustomizationTranslation.php&line=1", "ajax": false, "filename": "ThemeCustomizationTranslation.php", "line": "?"}}, "Webkul\\Marketplace\\Models\\Product\\Product": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FModels%2FProduct%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Webkul\\Product\\Models\\Product": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Webkul\\Marketplace\\Models\\Core\\Visit": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FModels%2FCore%2FVisit.php&line=1", "ajax": false, "filename": "Visit.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeTranslation.php&line=1", "ajax": false, "filename": "AttributeTranslation.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductImage": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductImage.php&line=1", "ajax": false, "filename": "ProductImage.php", "line": "?"}}, "Webkul\\Customer\\Models\\CustomerGroup": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FCustomer%2Fsrc%2FModels%2FCustomerGroup.php&line=1", "ajax": false, "filename": "CustomerGroup.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductInventoryIndex": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductInventoryIndex.php&line=1", "ajax": false, "filename": "ProductInventoryIndex.php", "line": "?"}}}, "count": 142, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/california-gold-nutrition-sport-creatine-monohydrate-unflavored-1-lb-454-g", "action_name": "shop.product_or_category.index", "controller_action": "Webkul\\Marketplace\\Http\\Controllers\\Shop\\ProductsCategoriesProxyController@index", "uri": "GET {fallbackPlaceholder}", "controller": "Webkul\\Marketplace\\Http\\Controllers\\Shop\\ProductsCategoriesProxyController@index<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FHttp%2FControllers%2FShop%2FProductsCategoriesProxyController.php&line=47\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FHttp%2FControllers%2FShop%2FProductsCategoriesProxyController.php&line=47\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Marketplace/src/Http/Controllers/Shop/ProductsCategoriesProxyController.php:47-156</a>", "middleware": "web, theme, locale, currency, marketplace, cache.response", "duration": "1.29s", "peak_memory": "52MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-718965825 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-718965825\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-431928277 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-431928277\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1442266794 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-speculation-tags</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">null</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"748 characters\">_gcl_au=1.1.1178683659.1752735529; XSRF-TOKEN=eyJpdiI6IkV0UjV3VmVjSWhjSUpOazUrQ3VhZlE9PSIsInZhbHVlIjoiM2dBdVNrTndMendUWVhlNVQ5YTB6dDRXMHpiUVQ2M2sxdUJzNjZoNlhDSGcyTnRuYnhNSXR3Z29VQ1JSeEJBVS9hdndPQytxd2dmSXRSdnE0bVNBbytiUFlrcHVvZnY0UHBkYjhzay9HRk1qSlJaWTZEV0pqdzNBd09icExkeXIiLCJtYWMiOiI4YzQyYTg3M2NkZGM2ZWI3MWZkZTI3NWIyZDM0MzA1YWViYmNkZWZkZjk1MThmMjRhZDkwNDg4ZmQ2MjU1ZDkyIiwidGFnIjoiIn0%3D; bagisto_session=eyJpdiI6InhIWFNiT3VuQThjeStDQktrY3VUNkE9PSIsInZhbHVlIjoiazBQK3pCYkFRVUY2dnlWUExDYUtJNlNYOCs5NFN2VEZTL0hyNjY0UVMxODd1dVZZSERKd1VjZE5MTi9ZdGhVM1RHTnFGVlFKMXBJMEVzd3c4L2ViZHpINzk5c0ZzRE1JYS8wZWoyV0dnS0dSd0NuZ29iRWNjQnkxZlZuVHliZnkiLCJtYWMiOiI1MDhjYjRkYmY0NGY2ODZiNWEyN2RkNWRlMTQxNWEyNTYyYzA0YjM3NmYwYTU5NGI5ODQ3NDg3MWI2NmRkYzA4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1442266794\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1830160633 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_gcl_au</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zkGTTvhSKLJlVdv7c7khny3gz8R0QPgdNfWirEBi</span>\"\n  \"<span class=sf-dump-key>bagisto_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">d6NyqSDpTQRmraq0TUwWTC4Y0P4XwPCUN8xQsY9a</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1830160633\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-803662518 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 12:50:17 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-803662518\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1758909467 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zkGTTvhSKLJlVdv7c7khny3gz8R0QPgdNfWirEBi</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"84 characters\">http://127.0.0.1:8000/storage/channel/1/bcBlnQFTRShae8Jv8Ij5G6dg4vwunt8vFhyn0zBg.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1758909467\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/california-gold-nutrition-sport-creatine-monohydrate-unflavored-1-lb-454-g", "action_name": "shop.product_or_category.index", "controller_action": "Webkul\\Marketplace\\Http\\Controllers\\Shop\\ProductsCategoriesProxyController@index"}, "badge": null}}