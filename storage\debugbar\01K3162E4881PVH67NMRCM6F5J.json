{"__meta": {"id": "01K3162E4881PVH67NMRCM6F5J", "datetime": "2025-08-19 18:20:08", "utime": **********.137092, "method": "GET", "uri": "/api/products?tag=21&sort=created_at-desc&limit=12", "ip": "127.0.0.1"}, "modules": {"count": 5, "modules": [{"name": "Webkul\\Attribute", "models": ["Webkul\\Attribute\\Models\\Attribute (32)", "Webkul\\Attribute\\Models\\AttributeFamily (1)"], "views": [], "queries": [{"sql": "select `id`, `code`, `value_per_channel`, `value_per_locale`, `type`, `is_filterable`, `is_configurable` from `attributes` where `code` in ('tag', 'sort', 'limit', 'channel_id', 'status', 'visible_individually', 'url_key')", "duration": 1, "duration_str": "1s", "connection": "mditems"}, {"sql": "select * from `attributes` where `code` = 'created_at'", "duration": 1, "duration_str": "1s", "connection": "mditems"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (1)", "duration": 0.96, "duration_str": "960ms", "connection": "mditems"}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 1", "duration": 1.46, "duration_str": "1.46s", "connection": "mditems"}]}, {"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\CoreConfig (1)", "Webkul\\Core\\Models\\Locale (1)", "Webkul\\Core\\Models\\Currency (3)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('127.0.0.1:8000', 'http://127.0.0.1:8000', 'https://127.0.0.1:8000')", "duration": 1, "duration_str": "1s", "connection": "mditems"}, {"sql": "select * from `channels` limit 1", "duration": 1.26, "duration_str": "1.26s", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'marketplace.settings.general.status' and `channel_code` = 'default'", "duration": 1.3, "duration_str": "1.3s", "connection": "mditems"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.77, "duration_str": "770ms", "connection": "mditems"}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "duration": 1.08, "duration_str": "1.08s", "connection": "mditems"}, {"sql": "select * from `currencies` where `code` = 'USD'", "duration": 1.61, "duration_str": "1.61s", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'catalog.products.search.engine'", "duration": 0.68, "duration_str": "680ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'catalog.products.storefront.products_per_page' and `channel_code` = 'default'", "duration": 1.28, "duration_str": "1.28s", "connection": "mditems"}, {"sql": "select * from `currency_exchange_rates` where `target_currency` = 1", "duration": 0.79, "duration_str": "790ms", "connection": "mditems"}]}, {"name": "Webkul\\Customer", "models": ["Webkul\\Customer\\Models\\CustomerGroup (1)"], "views": [], "queries": [{"sql": "select * from `customer_groups` where `code` = 'guest'", "duration": 0.81, "duration_str": "810ms", "connection": "mditems"}]}, {"name": "Webkul\\Product", "models": ["Webkul\\Product\\Models\\ProductImage (4)", "Webkul\\Product\\Models\\ProductAttributeValue (55)", "Webkul\\Product\\Models\\ProductPriceIndex (6)", "Webkul\\Product\\Models\\ProductInventoryIndex (2)"], "views": [], "queries": [{"sql": "select * from `product_images` where `product_images`.`product_id` in (2, 6) order by `position` asc", "duration": 0.88, "duration_str": "880ms", "connection": "mditems"}, {"sql": "select * from `product_videos` where `product_videos`.`product_id` in (2, 6) order by `position` asc", "duration": 0.7, "duration_str": "700ms", "connection": "mditems"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (2, 6)", "duration": 1.05, "duration_str": "1.05s", "connection": "mditems"}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` in (2, 6)", "duration": 1.69, "duration_str": "1.69s", "connection": "mditems"}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` in (2, 6)", "duration": 0.85, "duration_str": "850ms", "connection": "mditems"}, {"sql": "select * from `product_reviews` where `product_reviews`.`product_id` in (2, 6)", "duration": 0.76, "duration_str": "760ms", "connection": "mditems"}]}, {"name": "Webkul\\Marketplace", "models": ["Webkul\\Marketplace\\Models\\Product\\Product (2)", "Webkul\\Marketplace\\Models\\Product (2)", "Webkul\\Marketplace\\Models\\Seller (2)"], "views": [], "queries": [{"sql": "select distinct `products`.* from `products` left join `products` as `variants` on COALESCE(variants.parent_id, variants.id) = `products`.`id` left join `product_price_indices` on `products`.`id` = `product_price_indices`.`product_id` and `product_price_indices`.`customer_group_id` = 1 left join `marketplace_products` on `products`.`id` = `marketplace_products`.`product_id` left join `marketplace_sellers` on `marketplace_products`.`marketplace_seller_id` = `marketplace_sellers`.`id` left join `product_channels` on `products`.`id` = `product_channels`.`product_id` left join `product_attribute_values` as `url_key_product_attribute_values` on `products`.`id` = `url_key_product_attribute_values`.`product_id` left join `product_attribute_values` as `visible_individually_product_attribute_values` on `products`.`id` = `visible_individually_product_attribute_values`.`product_id` left join `product_attribute_values` as `status_product_attribute_values` on `products`.`id` = `status_product_attribute_values`.`product_id` left join `product_attribute_values` as `tag_product_attribute_values` on `products`.`id` = `tag_product_attribute_values`.`product_id` and `tag_product_attribute_values`.`attribute_id` = 33 left join `product_attribute_values` as `tag_variant_attribute_values` on `variants`.`id` = `tag_variant_attribute_values`.`product_id` and `tag_variant_attribute_values`.`attribute_id` = 33 where (`marketplace_products`.`id` is null or `marketplace_products`.`is_owner` = 0 or (`marketplace_sellers`.`is_suspended` = 0 and `marketplace_sellers`.`is_approved` = 1 and `marketplace_products`.`is_approved` = 1)) and `product_channels`.`channel_id` = '1' and `url_key_product_attribute_values`.`attribute_id` = 3 and `url_key_product_attribute_values`.`text_value` is not null and `visible_individually_product_attribute_values`.`attribute_id` = 7 and `visible_individually_product_attribute_values`.`boolean_value` = 1 and `status_product_attribute_values`.`attribute_id` = 8 and `status_product_attribute_values`.`boolean_value` = 1 and ((`tag_product_attribute_values`.`text_value` in ('21')) or (`tag_variant_attribute_values`.`text_value` in ('21'))) group by `products`.`id`, `products`.`id` order by `products`.`created_at` desc limit 12 offset 0", "duration": 12.22, "duration_str": "12.22s", "connection": "mditems"}, {"sql": "select * from `products` where `products`.`parent_id` in (2, 6)", "duration": 0.77, "duration_str": "770ms", "connection": "mditems"}, {"sql": "select * from `marketplace_products` where `product_id` = 6 and `is_owner` = 1", "duration": 1.13, "duration_str": "1.13s", "connection": "mditems"}, {"sql": "select * from `marketplace_sellers` where `marketplace_sellers`.`id` = 4 limit 1", "duration": 1.59, "duration_str": "1.59s", "connection": "mditems"}, {"sql": "select * from `marketplace_products` where `product_id` = 2 and `is_owner` = 1", "duration": 1.7, "duration_str": "1.7s", "connection": "mditems"}, {"sql": "select * from `marketplace_sellers` where `marketplace_sellers`.`id` = 4 limit 1", "duration": 0.92, "duration_str": "920ms", "connection": "mditems"}]}]}, "messages": {"count": 4, "messages": [{"message": "[18:20:08] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.069551, "xdebug_link": null, "collector": "log"}, {"message": "[18:20:08] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.069852, "xdebug_link": null, "collector": "log"}, {"message": "[18:20:08] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.113262, "xdebug_link": null, "collector": "log"}, {"message": "[18:20:08] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.130651, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.646076, "end": **********.158196, "duration": 0.5121200084686279, "duration_str": "512ms", "measures": [{"label": "Booting", "start": **********.646076, "relative_start": 0, "end": **********.931916, "relative_end": **********.931916, "duration": 0.****************, "duration_str": "286ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.931927, "relative_start": 0.*****************, "end": **********.158199, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "226ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.947634, "relative_start": 0.****************, "end": **********.951526, "relative_end": **********.951526, "duration": 0.0038919448852539062, "duration_str": "3.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.070202, "relative_start": 0.*****************, "end": **********.134381, "relative_end": **********.134381, "duration": 0.****************, "duration_str": "64.18ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: shop::products.prices.index", "start": **********.112988, "relative_start": 0.*****************, "end": **********.112988, "relative_end": **********.112988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.index", "start": **********.130246, "relative_start": 0.*****************, "end": **********.130246, "relative_end": **********.130246, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 45778224, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 2, "nb_templates": 2, "templates": [{"name": "shop::products.prices.index", "param_count": null, "params": [], "start": **********.112946, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/products/prices/index.blade.phpshop::products.prices.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "shop::products.prices.index", "param_count": null, "params": [], "start": **********.130182, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/products/prices/index.blade.phpshop::products.prices.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}]}, "queries": {"count": 24, "nb_statements": 24, "nb_visible_statements": 24, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.05301999999999999, "accumulated_duration_str": "53.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "installer_locale", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "theme", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Theme.php", "line": 32}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.9646008, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mditems", "explain": null, "start_percent": 0, "width_percent": 1.452}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "currency", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "installer_locale", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 40}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.967598, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mditems", "explain": null, "start_percent": 1.452, "width_percent": 2.037}, {"sql": "select * from `currencies` where `code` = 'USD'", "type": "query", "params": [], "bindings": ["USD"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 296}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 295}], "start": **********.97423, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "mditems", "explain": null, "start_percent": 3.489, "width_percent": 3.037}, {"sql": "select * from `core_config` where `code` = 'catalog.products.search.engine'", "type": "query", "params": [], "bindings": ["catalog.products.search.engine"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.982281, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 6.526, "width_percent": 1.283}, {"sql": "select * from `core_config` where `code` = 'catalog.products.storefront.products_per_page' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["catalog.products.storefront.products_per_page", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.990689, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 7.808, "width_percent": 2.414}, {"sql": "select * from `customer_groups` where `code` = 'guest'", "type": "query", "params": [], "bindings": ["guest"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Core.php", "line": 746}, {"index": 19, "namespace": null, "name": "packages/Webkul/Customer/src/Repositories/CustomerRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Customer\\src\\Repositories\\CustomerRepository.php", "line": 41}], "start": **********.997553, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "mditems", "explain": null, "start_percent": 10.223, "width_percent": 1.528}, {"sql": "select `id`, `code`, `value_per_channel`, `value_per_locale`, `type`, `is_filterable`, `is_configurable` from `attributes` where `code` in ('tag', 'sort', 'limit', 'channel_id', 'status', 'visible_individually', 'url_key')", "type": "query", "params": [], "bindings": ["tag", "sort", "limit", "channel_id", "status", "visible_individually", "url_key"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeRepository.php", "line": 198}, {"index": 17, "namespace": null, "name": "packages/Webkul/Marketplace/src/Repositories/Product/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Repositories\\Product\\ProductRepository.php", "line": 109}, {"index": 18, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 1033}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 485}], "start": **********.002438, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mditems", "explain": null, "start_percent": 11.75, "width_percent": 1.886}, {"sql": "select * from `attributes` where `code` = 'created_at'", "type": "query", "params": [], "bindings": ["created_at"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/Marketplace/src/Repositories/Product/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Repositories\\Product\\ProductRepository.php", "line": 200}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 1033}], "start": **********.0081742, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "mditems", "explain": null, "start_percent": 13.636, "width_percent": 1.886}, {"sql": "select count(*) as aggregate from (select distinct `products`.* from `products` left join `products` as `variants` on COALESCE(variants.parent_id, variants.id) = `products`.`id` left join `product_price_indices` on `products`.`id` = `product_price_indices`.`product_id` and `product_price_indices`.`customer_group_id` = 1 left join `marketplace_products` on `products`.`id` = `marketplace_products`.`product_id` left join `marketplace_sellers` on `marketplace_products`.`marketplace_seller_id` = `marketplace_sellers`.`id` left join `product_channels` on `products`.`id` = `product_channels`.`product_id` left join `product_attribute_values` as `url_key_product_attribute_values` on `products`.`id` = `url_key_product_attribute_values`.`product_id` left join `product_attribute_values` as `visible_individually_product_attribute_values` on `products`.`id` = `visible_individually_product_attribute_values`.`product_id` left join `product_attribute_values` as `status_product_attribute_values` on `products`.`id` = `status_product_attribute_values`.`product_id` left join `product_attribute_values` as `tag_product_attribute_values` on `products`.`id` = `tag_product_attribute_values`.`product_id` and `tag_product_attribute_values`.`attribute_id` = 33 left join `product_attribute_values` as `tag_variant_attribute_values` on `variants`.`id` = `tag_variant_attribute_values`.`product_id` and `tag_variant_attribute_values`.`attribute_id` = 33 where (`marketplace_products`.`id` is null or `marketplace_products`.`is_owner` = 0 or (`marketplace_sellers`.`is_suspended` = 0 and `marketplace_sellers`.`is_approved` = 1 and `marketplace_products`.`is_approved` = 1)) and `product_channels`.`channel_id` = '1' and `url_key_product_attribute_values`.`attribute_id` = 3 and `url_key_product_attribute_values`.`text_value` is not null and `visible_individually_product_attribute_values`.`attribute_id` = 7 and `visible_individually_product_attribute_values`.`boolean_value` = 1 and `status_product_attribute_values`.`attribute_id` = 8 and `status_product_attribute_values`.`boolean_value` = 1 and ((`tag_product_attribute_values`.`text_value` in ('21')) or (`tag_variant_attribute_values`.`text_value` in ('21'))) group by `products`.`id`, `products`.`id`) as `aggregate_table`", "type": "query", "params": [], "bindings": [1, 33, 33, 0, 0, 1, 1, "1", 3, 7, 1, 8, 1, "21", "21"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 17, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 18, "namespace": null, "name": "packages/Webkul/Marketplace/src/Repositories/Product/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Repositories\\Product\\ProductRepository.php", "line": 240}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 215}, {"index": 20, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.0112581, "duration": 0.01732, "duration_str": "17.32ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mditems", "explain": null, "start_percent": 15.522, "width_percent": 32.667}, {"sql": "select distinct `products`.* from `products` left join `products` as `variants` on COALESCE(variants.parent_id, variants.id) = `products`.`id` left join `product_price_indices` on `products`.`id` = `product_price_indices`.`product_id` and `product_price_indices`.`customer_group_id` = 1 left join `marketplace_products` on `products`.`id` = `marketplace_products`.`product_id` left join `marketplace_sellers` on `marketplace_products`.`marketplace_seller_id` = `marketplace_sellers`.`id` left join `product_channels` on `products`.`id` = `product_channels`.`product_id` left join `product_attribute_values` as `url_key_product_attribute_values` on `products`.`id` = `url_key_product_attribute_values`.`product_id` left join `product_attribute_values` as `visible_individually_product_attribute_values` on `products`.`id` = `visible_individually_product_attribute_values`.`product_id` left join `product_attribute_values` as `status_product_attribute_values` on `products`.`id` = `status_product_attribute_values`.`product_id` left join `product_attribute_values` as `tag_product_attribute_values` on `products`.`id` = `tag_product_attribute_values`.`product_id` and `tag_product_attribute_values`.`attribute_id` = 33 left join `product_attribute_values` as `tag_variant_attribute_values` on `variants`.`id` = `tag_variant_attribute_values`.`product_id` and `tag_variant_attribute_values`.`attribute_id` = 33 where (`marketplace_products`.`id` is null or `marketplace_products`.`is_owner` = 0 or (`marketplace_sellers`.`is_suspended` = 0 and `marketplace_sellers`.`is_approved` = 1 and `marketplace_products`.`is_approved` = 1)) and `product_channels`.`channel_id` = '1' and `url_key_product_attribute_values`.`attribute_id` = 3 and `url_key_product_attribute_values`.`text_value` is not null and `visible_individually_product_attribute_values`.`attribute_id` = 7 and `visible_individually_product_attribute_values`.`boolean_value` = 1 and `status_product_attribute_values`.`attribute_id` = 8 and `status_product_attribute_values`.`boolean_value` = 1 and ((`tag_product_attribute_values`.`text_value` in ('21')) or (`tag_variant_attribute_values`.`text_value` in ('21'))) group by `products`.`id`, `products`.`id` order by `products`.`created_at` desc limit 12 offset 0", "type": "query", "params": [], "bindings": [1, 33, 33, 0, 0, 1, 1, "1", 3, 7, 1, 8, 1, "21", "21"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 17, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 18, "namespace": null, "name": "packages/Webkul/Marketplace/src/Repositories/Product/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Repositories\\Product\\ProductRepository.php", "line": 240}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 215}, {"index": 20, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.0303142, "duration": 0.01222, "duration_str": "12.22ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mditems", "explain": null, "start_percent": 48.189, "width_percent": 23.048}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Marketplace/src/Repositories/Product/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Repositories\\Product\\ProductRepository.php", "line": 240}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 215}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.044859, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mditems", "explain": null, "start_percent": 71.237, "width_percent": 1.811}, {"sql": "select * from `product_images` where `product_images`.`product_id` in (2, 6) order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Marketplace/src/Repositories/Product/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Repositories\\Product\\ProductRepository.php", "line": 240}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 215}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.048095, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mditems", "explain": null, "start_percent": 73.048, "width_percent": 1.66}, {"sql": "select * from `product_videos` where `product_videos`.`product_id` in (2, 6) order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Marketplace/src/Repositories/Product/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Repositories\\Product\\ProductRepository.php", "line": 240}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 215}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.050509, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mditems", "explain": null, "start_percent": 74.708, "width_percent": 1.32}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (2, 6)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Marketplace/src/Repositories/Product/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Repositories\\Product\\ProductRepository.php", "line": 240}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 215}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.052736, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mditems", "explain": null, "start_percent": 76.028, "width_percent": 1.98}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` in (2, 6)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Marketplace/src/Repositories/Product/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Repositories\\Product\\ProductRepository.php", "line": 240}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 215}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.056868, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mditems", "explain": null, "start_percent": 78.008, "width_percent": 3.187}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` in (2, 6)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Marketplace/src/Repositories/Product/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Repositories\\Product\\ProductRepository.php", "line": 240}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 215}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.060403, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mditems", "explain": null, "start_percent": 81.196, "width_percent": 1.603}, {"sql": "select * from `product_reviews` where `product_reviews`.`product_id` in (2, 6)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Marketplace/src/Repositories/Product/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Repositories\\Product\\ProductRepository.php", "line": 240}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 215}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.062695, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mditems", "explain": null, "start_percent": 82.799, "width_percent": 1.433}, {"sql": "select * from `products` where `products`.`parent_id` in (2, 6)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Marketplace/src/Repositories/Product/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Repositories\\Product\\ProductRepository.php", "line": 240}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 215}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.064749, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mditems", "explain": null, "start_percent": 84.232, "width_percent": 1.452}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeRepository.php", "line": 213}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 514}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}], "start": **********.078765, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "AttributeFamily.php:53", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=53", "ajax": false, "filename": "AttributeFamily.php", "line": "53"}, "connection": "mditems", "explain": null, "start_percent": 85.685, "width_percent": 2.754}, {"sql": "select * from `marketplace_products` where `product_id` = 6 and `is_owner` = 1", "type": "query", "params": [], "bindings": [6, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": null, "name": "packages/Webkul/Marketplace/src/Types/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Types\\Simple.php", "line": 26}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 505}], "start": **********.1001759, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 88.438, "width_percent": 2.131}, {"sql": "select * from `marketplace_sellers` where `marketplace_sellers`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Marketplace/src/Types/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Types\\Simple.php", "line": 33}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 505}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 157}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 145}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 44}], "start": **********.1030369, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "Simple.php:33", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Marketplace/src/Types/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Types\\Simple.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FTypes%2FSimple.php&line=33", "ajax": false, "filename": "Simple.php", "line": "33"}, "connection": "mditems", "explain": null, "start_percent": 90.57, "width_percent": 2.999}, {"sql": "select * from `currency_exchange_rates` where `target_currency` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Core.php", "line": 457}, {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Core.php", "line": 479}], "start": **********.10912, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 93.568, "width_percent": 1.49}, {"sql": "select * from `marketplace_products` where `product_id` = 2 and `is_owner` = 1", "type": "query", "params": [], "bindings": [2, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": null, "name": "packages/Webkul/Marketplace/src/Types/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Types\\Simple.php", "line": 26}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 505}], "start": **********.121267, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 95.058, "width_percent": 3.206}, {"sql": "select * from `marketplace_sellers` where `marketplace_sellers`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Marketplace/src/Types/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Types\\Simple.php", "line": 33}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 505}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 157}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 145}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 44}], "start": **********.1255229, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Simple.php:33", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Marketplace/src/Types/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Types\\Simple.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FTypes%2FSimple.php&line=33", "ajax": false, "filename": "Simple.php", "line": "33"}, "connection": "mditems", "explain": null, "start_percent": 98.265, "width_percent": 1.735}]}, "models": {"data": {"Webkul\\Product\\Models\\ProductAttributeValue": {"value": 55, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductAttributeValue.php&line=1", "ajax": false, "filename": "ProductAttributeValue.php", "line": "?"}}, "Webkul\\Attribute\\Models\\Attribute": {"value": 32, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductPriceIndex": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductPriceIndex.php&line=1", "ajax": false, "filename": "ProductPriceIndex.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductImage": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductImage.php&line=1", "ajax": false, "filename": "ProductImage.php", "line": "?"}}, "Webkul\\Core\\Models\\Currency": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Webkul\\Marketplace\\Models\\Product\\Product": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FModels%2FProduct%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductInventoryIndex": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductInventoryIndex.php&line=1", "ajax": false, "filename": "ProductInventoryIndex.php", "line": "?"}}, "Webkul\\Marketplace\\Models\\Product": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Webkul\\Marketplace\\Models\\Seller": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FModels%2FSeller.php&line=1", "ajax": false, "filename": "Seller.php", "line": "?"}}, "Webkul\\Core\\Models\\Locale": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Customer\\Models\\CustomerGroup": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FCustomer%2Fsrc%2FModels%2FCustomerGroup.php&line=1", "ajax": false, "filename": "CustomerGroup.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeFamily": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=1", "ajax": false, "filename": "AttributeFamily.php", "line": "?"}}}, "count": 111, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/api/products?limit=12&sort=created_at-desc&tag=21", "action_name": "shop.api.products.index", "controller_action": "Webkul\\Shop\\Http\\Controllers\\API\\ProductController@index", "uri": "GET api/products", "controller": "Webkul\\Shop\\Http\\Controllers\\API\\ProductController@index<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FAPI%2FProductController.php&line=26\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/products", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FAPI%2FProductController.php&line=26\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php:26-56</a>", "middleware": "web, shop, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance", "duration": "520ms", "peak_memory": "46MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-520856696 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>tag</span>\" => \"<span class=sf-dump-str title=\"2 characters\">21</span>\"\n  \"<span class=sf-dump-key>sort</span>\" => \"<span class=sf-dump-str title=\"15 characters\">created_at-desc</span>\"\n  \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str title=\"2 characters\">12</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-520856696\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1752194240 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1752194240\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImtOZHZweWljcTM4MlJJbVRPVnZuWmc9PSIsInZhbHVlIjoiNjBzZnlrZmpqVDRscndlcnBRbmpWdGtva2x6Q0FIdXZVK1hMSlRzRU45WlhLaVhjbk9OUnJyaVNXRkZLQVNTZHJqZ1B6MHpaazZ2ZzlXMkRYYkx3RVlzM24xTVFxSHQxN1N0NTczSVRmWXJVcWMwSTBYNHlvQ2hPaC9VVVYzenIiLCJtYWMiOiJhOTdlZmEwNjliOTA5YmYyMDdhZDQ4NjI1NDQxZmE5YjU4MDM3NTg4NGIwNWM4N2QzN2Y4ZmNlZjI1NDUwM2NlIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"748 characters\">_gcl_au=1.1.**********.**********; XSRF-TOKEN=eyJpdiI6ImtOZHZweWljcTM4MlJJbVRPVnZuWmc9PSIsInZhbHVlIjoiNjBzZnlrZmpqVDRscndlcnBRbmpWdGtva2x6Q0FIdXZVK1hMSlRzRU45WlhLaVhjbk9OUnJyaVNXRkZLQVNTZHJqZ1B6MHpaazZ2ZzlXMkRYYkx3RVlzM24xTVFxSHQxN1N0NTczSVRmWXJVcWMwSTBYNHlvQ2hPaC9VVVYzenIiLCJtYWMiOiJhOTdlZmEwNjliOTA5YmYyMDdhZDQ4NjI1NDQxZmE5YjU4MDM3NTg4NGIwNWM4N2QzN2Y4ZmNlZjI1NDUwM2NlIiwidGFnIjoiIn0%3D; bagisto_session=eyJpdiI6InVQdmtlWnpIWDRSMDNiMU92amlDbVE9PSIsInZhbHVlIjoiaXUvOStRbVRpT1RpWUFsL1hITW1pN1dUTVB1MFh3dWVVN0NTdGczVzh5bzZuRHRFcDJ1UWRBeG9OeHNkSGFyNU5JaGg5TzlSQjA1MVRmTzBSVUxRdGpVSWJhSVZvTk01U3hwajhRaVZ1Ylc0SGpibkt1MjlOOWRkYzJpNlRMeDgiLCJtYWMiOiJhMzEyOTE3NGQxYWE0ZWMyOGYxOTRlZWFlNjQzMTc3ZWE2YWE5Mjc5ZmVkZTQ3NDZlNDdkMDhlZDMwMTg3ZTg0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-524117785 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_gcl_au</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zkGTTvhSKLJlVdv7c7khny3gz8R0QPgdNfWirEBi</span>\"\n  \"<span class=sf-dump-key>bagisto_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">d6NyqSDpTQRmraq0TUwWTC4Y0P4XwPCUN8xQsY9a</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-524117785\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1816292462 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 12:50:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1816292462\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1090461360 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zkGTTvhSKLJlVdv7c7khny3gz8R0QPgdNfWirEBi</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"84 characters\">http://127.0.0.1:8000/storage/channel/1/ghBTCHgGKvmgiRQ6BP8w0XynTgOkkSQiXx69toiA.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1090461360\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/api/products?limit=12&sort=created_at-desc&tag=21", "action_name": "shop.api.products.index", "controller_action": "Webkul\\Shop\\Http\\Controllers\\API\\ProductController@index"}, "badge": null}}