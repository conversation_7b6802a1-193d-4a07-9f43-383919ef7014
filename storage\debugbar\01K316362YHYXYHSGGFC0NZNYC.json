{"__meta": {"id": "01K316362YHYXYHSGGFC0NZNYC", "datetime": "2025-08-19 18:20:32", "utime": **********.671097, "method": "GET", "uri": "/cache/original/product/2/OcBBpN1b9NUNBvOTsKRrZdetu5p62ahQ83EmngyC.webp", "ip": "127.0.0.1"}, "modules": {"count": 1, "modules": [{"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\CoreConfig (1)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('127.0.0.1:8000', 'http://127.0.0.1:8000', 'https://127.0.0.1:8000')", "duration": 0.64, "duration_str": "640ms", "connection": "mditems"}, {"sql": "select * from `channels` limit 1", "duration": 0.48, "duration_str": "480ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'marketplace.settings.general.status' and `channel_code` = 'default'", "duration": 0.62, "duration_str": "620ms", "connection": "mditems"}]}]}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.394531, "end": **********.679739, "duration": 0.28520798683166504, "duration_str": "285ms", "measures": [{"label": "Booting", "start": **********.394531, "relative_start": 0, "end": **********.638642, "relative_end": **********.638642, "duration": 0.****************, "duration_str": "244ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.638653, "relative_start": 0.*****************, "end": **********.679741, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "41.09ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.64944, "relative_start": 0.*****************, "end": **********.654145, "relative_end": **********.654145, "duration": 0.004704952239990234, "duration_str": "4.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.669362, "relative_start": 0.*****************, "end": **********.669468, "relative_end": **********.669468, "duration": 0.00010585784912109375, "duration_str": "106μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.669485, "relative_start": 0.*****************, "end": **********.669497, "relative_end": **********.669497, "duration": 1.1920928955078125e-05, "duration_str": "12μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "36MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/cache/original/product/2/OcBBpN1b9NUNBvOTsKRrZdetu5p62ahQ83EmngyC.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "286ms", "peak_memory": "38MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1286771841 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1286771841\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-223356066 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-223356066\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1053709508 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"96 characters\">http://127.0.0.1:8000/california-gold-nutrition-sport-creatine-monohydrate-unflavored-1-lb-454-g</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"748 characters\">_gcl_au=1.1.1178683659.1752735529; XSRF-TOKEN=eyJpdiI6IkhyVDBhTXYxRkY4Tk5udGdOSFkzRWc9PSIsInZhbHVlIjoiMEQzU2FnVzhrdEdWenVoZi90MURsd3l2ZnVvOWpJN0hYcFhUK2xXNTJvT29wbCtvSGJscEIvd2dqenBkdVdoYWN6a1VrTFdaVWhvZDlBRno5ZnJqUU5CT29ybHYzUHA2Vm9BMXB1MGNUT21MZEsvaUJIMmxKUGdSbWpIZ3dzVVEiLCJtYWMiOiJiMzBiMjFkYjQ0OTMwYWYzZjBmYmQxNzU3NGE4MTFhNzZjMjMyZGY1ZWExNWI2NDM3MDVhZDYwNmFkY2Y4ZGRlIiwidGFnIjoiIn0%3D; bagisto_session=eyJpdiI6IjA0UnB6K2ZORUd1NTYrS1Zxd1lDU2c9PSIsInZhbHVlIjoib1ZrWVhUTjlMMUJ3enQ1eXZJWENTSlFiODczSjd3dU5LQzd6S1U4K0c3RDRhS28rYndQRnRLN09SeXBvcW9sQUF4S2pIZW0rZWZhVWM0TzNOeTh1MlhwZnE4WmhvbTY4Z1N0TjlKcUpKUWpvTUZEdThjU1ljWVlMT3pJam9TVEoiLCJtYWMiOiJmZTgyYWMzZDU1ZjU5ODViNDNkMjlkY2MwZDIwZThlNjYwZWY2MTkzMDczMTQ0ZmQyNjZkNmQ4YTkzNDU4ZDQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1053709508\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1983516582 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_gcl_au</span>\" => \"<span class=sf-dump-str title=\"25 characters\">1.1.1178683659.1752735529</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkhyVDBhTXYxRkY4Tk5udGdOSFkzRWc9PSIsInZhbHVlIjoiMEQzU2FnVzhrdEdWenVoZi90MURsd3l2ZnVvOWpJN0hYcFhUK2xXNTJvT29wbCtvSGJscEIvd2dqenBkdVdoYWN6a1VrTFdaVWhvZDlBRno5ZnJqUU5CT29ybHYzUHA2Vm9BMXB1MGNUT21MZEsvaUJIMmxKUGdSbWpIZ3dzVVEiLCJtYWMiOiJiMzBiMjFkYjQ0OTMwYWYzZjBmYmQxNzU3NGE4MTFhNzZjMjMyZGY1ZWExNWI2NDM3MDVhZDYwNmFkY2Y4ZGRlIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>bagisto_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjA0UnB6K2ZORUd1NTYrS1Zxd1lDU2c9PSIsInZhbHVlIjoib1ZrWVhUTjlMMUJ3enQ1eXZJWENTSlFiODczSjd3dU5LQzd6S1U4K0c3RDRhS28rYndQRnRLN09SeXBvcW9sQUF4S2pIZW0rZWZhVWM0TzNOeTh1MlhwZnE4WmhvbTY4Z1N0TjlKcUpKUWpvTUZEdThjU1ljWVlMT3pJam9TVEoiLCJtYWMiOiJmZTgyYWMzZDU1ZjU5ODViNDNkMjlkY2MwZDIwZThlNjYwZWY2MTkzMDczMTQ0ZmQyNjZkNmQ4YTkzNDU4ZDQxIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1983516582\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-745717688 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">240796</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">3461da04b66157ff8303c5ec7b1839b6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 12:50:32 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-745717688\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-293084167 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-293084167\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/cache/original/product/2/OcBBpN1b9NUNBvOTsKRrZdetu5p62ahQ83EmngyC.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}