{"__meta": {"id": "01K316B9D15TC515PQR4RJ424W", "datetime": "2025-08-19 18:24:58", "utime": **********.210041, "method": "GET", "uri": "/storage/theme/5/BvVWSMy5u18lsS3blsfAgYkwPQ1BhpsuGIAT5PF2.webp", "ip": "127.0.0.1"}, "modules": {"count": 4, "modules": [{"name": "Webkul\\Attribute", "models": ["Webkul\\Attribute\\Models\\Attribute (1)", "Webkul\\Attribute\\Models\\AttributeOption (3)"], "views": [], "queries": [{"sql": "select * from `attributes` where `attributes`.`id` = 33 limit 1", "duration": 2.03, "duration_str": "2.03s", "connection": "mditems"}, {"sql": "select * from `attribute_options` where `attribute_id` = 33", "duration": 1.59, "duration_str": "1.59s", "connection": "mditems"}]}, {"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\CoreConfig (2)", "Webkul\\Core\\Models\\Locale (6)", "Webkul\\Core\\Models\\Currency (3)", "Webkul\\Core\\Models\\ChannelTranslation (1)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('127.0.0.1:8000', 'http://127.0.0.1:8000', 'https://127.0.0.1:8000')", "duration": 0.85, "duration_str": "850ms", "connection": "mditems"}, {"sql": "select * from `channels` limit 1", "duration": 1.14, "duration_str": "1.14s", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'marketplace.settings.general.status' and `channel_code` = 'default'", "duration": 0.7, "duration_str": "700ms", "connection": "mditems"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.82, "duration_str": "820ms", "connection": "mditems"}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "duration": 0.59, "duration_str": "590ms", "connection": "mditems"}, {"sql": "select * from `currencies` where `code` = 'USD'", "duration": 0.85, "duration_str": "850ms", "connection": "mditems"}, {"sql": "select * from `locales`", "duration": 0.47, "duration_str": "470ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'catalog.products.review.summary'", "duration": 0.82, "duration_str": "820ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'customer.settings.wishlist.wishlist_option'", "duration": 0.87, "duration_str": "870ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'catalog.products.settings.compare_option'", "duration": 0.75, "duration_str": "750ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'sales.checkout.shopping_cart.cart_page'", "duration": 0.8, "duration_str": "800ms", "connection": "mditems"}, {"sql": "select * from `locales` where `code` = 'en'", "duration": 1.03, "duration_str": "1.03s", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'general.content.custom_scripts.custom_css' and `channel_code` = 'default'", "duration": 0.8, "duration_str": "800ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'general.content.speculation_rules.enabled'", "duration": 0.68, "duration_str": "680ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'general.content.speculation_rules.prerender_enabled'", "duration": 0.64, "duration_str": "640ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'general.content.speculation_rules.prerender_eagerness'", "duration": 1.22, "duration_str": "1.22s", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'general.content.speculation_rules.prerender_ignore_urls'", "duration": 0.77, "duration_str": "770ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'general.content.speculation_rules.prerender_ignore_url_params'", "duration": 0.89, "duration_str": "890ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'general.content.speculation_rules.prefetch_enabled'", "duration": 0.75, "duration_str": "750ms", "connection": "mditems"}, {"sql": "select count(*) as aggregate from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 3.2, "duration_str": "3.2s", "connection": "mditems"}, {"sql": "select count(*) as aggregate from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "duration": 1.32, "duration_str": "1.32s", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'general.content.header_offer.title'", "duration": 0.81, "duration_str": "810ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'general.content.header_offer.redirection_link'", "duration": 0.91, "duration_str": "910ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'general.content.header_offer.redirection_title'", "duration": 0.91, "duration_str": "910ms", "connection": "mditems"}, {"sql": "select `name` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 and `code` = 'en' order by `name` asc limit 1", "duration": 1.12, "duration_str": "1.12s", "connection": "mditems"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 order by `name` asc", "duration": 0.72, "duration_str": "720ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'catalog.products.search.min_query_length'", "duration": 1.08, "duration_str": "1.08s", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'catalog.products.search.max_query_length'", "duration": 0.77, "duration_str": "770ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'catalog.products.settings.image_search'", "duration": 1.03, "duration_str": "1.03s", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'sales.checkout.mini_cart.display_mini_cart'", "duration": 0.98, "duration_str": "980ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'sales.checkout.my_cart.summary'", "duration": 0.84, "duration_str": "840ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'sales.checkout.mini_cart.offer_info'", "duration": 0.97, "duration_str": "970ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'sales.taxes.shopping_cart.display_prices'", "duration": 1.57, "duration_str": "1.57s", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'sales.taxes.shopping_cart.display_subtotal'", "duration": 1.01, "duration_str": "1.01s", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'general.design.categories.category_view'", "duration": 1.69, "duration_str": "1.69s", "connection": "mditems"}, {"sql": "select count(*) as aggregate from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.62, "duration_str": "620ms", "connection": "mditems"}, {"sql": "select count(*) as aggregate from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "duration": 0.42, "duration_str": "420ms", "connection": "mditems"}, {"sql": "select `name` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 and `code` = 'en' order by `name` asc limit 1", "duration": 0.57, "duration_str": "570ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'general.gdpr.settings.enabled' and `channel_code` = 'default' and `locale_code` = 'en'", "duration": 0.71, "duration_str": "710ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'customer.settings.newsletter.subscription'", "duration": 0.85, "duration_str": "850ms", "connection": "mditems"}, {"sql": "select * from `core_config` where `code` = 'general.content.custom_scripts.custom_javascript' and `channel_code` = 'default'", "duration": 0.83, "duration_str": "830ms", "connection": "mditems"}]}, {"name": "Webkul\\Theme", "models": ["Webkul\\Theme\\Models\\ThemeCustomization (15)", "Webkul\\Theme\\Models\\ThemeCustomizationTranslation (15)"], "views": [], "queries": [{"sql": "select * from `theme_customizations` where `status` = 1 and `channel_id` = 1 order by `sort_order` asc", "duration": 1.44, "duration_str": "1.44s", "connection": "mditems"}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14)", "duration": 0.57, "duration_str": "570ms", "connection": "mditems"}, {"sql": "select * from `theme_customizations` where `type` = 'services_content' and `status` = 1 and `theme_code` = 'default' and `channel_id` = 1", "duration": 1.71, "duration_str": "1.71s", "connection": "mditems"}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (12)", "duration": 0.62, "duration_str": "620ms", "connection": "mditems"}, {"sql": "select * from `theme_customizations` where `type` = 'footer_links' and `status` = 1 and `theme_code` = 'default' and `channel_id` = 1", "duration": 0.84, "duration_str": "840ms", "connection": "mditems"}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (11)", "duration": 0.51, "duration_str": "510ms", "connection": "mditems"}]}, {"name": "Webkul\\Marketplace", "models": [], "views": [], "queries": [{"sql": "select * from `visits` where (`method` = 'GET' and `url` = 'http://127.0.0.1:8000/storage/theme/5/BvVWSMy5u18lsS3blsfAgYkwPQ1BhpsuGIAT5PF2.webp' and `ip` = '127.0.0.1' and `visitor_id` is null and `visitor_type` is null and `channel_id` = 1) order by `created_at` desc limit 1", "duration": 0.94, "duration_str": "940ms", "connection": "mditems"}]}]}, "messages": {"count": 1, "messages": [{"message": "[18:24:57] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Core.php on line 961", "message_html": null, "is_string": false, "label": "warning", "time": **********.727712, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755608096.774732, "end": **********.244994, "duration": 1.470261812210083, "duration_str": "1.47s", "measures": [{"label": "Booting", "start": 1755608096.774732, "relative_start": 0, "end": **********.253717, "relative_end": **********.253717, "duration": 0.****************, "duration_str": "479ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.253735, "relative_start": 0.****************, "end": **********.244998, "relative_end": 4.0531158447265625e-06, "duration": 0.****************, "duration_str": "991ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.277162, "relative_start": 0.****************, "end": **********.291829, "relative_end": **********.291829, "duration": 0.014667034149169922, "duration_str": "14.67ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.415535, "relative_start": 0.****************, "end": **********.205654, "relative_end": **********.205654, "duration": 0.***************, "duration_str": "790ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: shop::home.index", "start": **********.419295, "relative_start": 0.****************, "end": **********.419295, "relative_end": **********.419295, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.carousel.index", "start": **********.466901, "relative_start": 0.****************, "end": **********.466901, "relative_end": **********.466901, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.media.images.lazy", "start": **********.467888, "relative_start": 0.6931560039520264, "end": **********.467888, "relative_end": **********.467888, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.categories.carousel", "start": **********.483575, "relative_start": 0.7088429927825928, "end": **********.483575, "relative_end": **********.483575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.categories.carousel", "start": **********.484554, "relative_start": 0.7098219394683838, "end": **********.484554, "relative_end": **********.484554, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.media.images.lazy", "start": **********.487218, "relative_start": 0.7124857902526855, "end": **********.487218, "relative_end": **********.487218, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.categories.carousel", "start": **********.488032, "relative_start": 0.7132999897003174, "end": **********.488032, "relative_end": **********.488032, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.carousel", "start": **********.499131, "relative_start": 0.7243988513946533, "end": **********.499131, "relative_end": **********.499131, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.carousel", "start": **********.500579, "relative_start": 0.7258470058441162, "end": **********.500579, "relative_end": **********.500579, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.cards.grid", "start": **********.501701, "relative_start": 0.7269690036773682, "end": **********.501701, "relative_end": **********.501701, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.card", "start": **********.50348, "relative_start": 0.7287478446960449, "end": **********.50348, "relative_end": **********.50348, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.media.images.lazy", "start": **********.505143, "relative_start": 0.7304108142852783, "end": **********.505143, "relative_end": **********.505143, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.ratings", "start": **********.515027, "relative_start": 0.7402949333190918, "end": **********.515027, "relative_end": **********.515027, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.media.images.lazy", "start": **********.568073, "relative_start": 0.7933409214019775, "end": **********.568073, "relative_end": **********.568073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.ratings", "start": **********.582955, "relative_start": 0.808222770690918, "end": **********.582955, "relative_end": **********.582955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.button.index", "start": **********.587968, "relative_start": 0.8132359981536865, "end": **********.587968, "relative_end": **********.587968, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.carousel", "start": **********.589022, "relative_start": 0.8142898082733154, "end": **********.589022, "relative_end": **********.589022, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.cards.grid", "start": **********.589509, "relative_start": 0.8147768974304199, "end": **********.589509, "relative_end": **********.589509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.carousel", "start": **********.595212, "relative_start": 0.8204798698425293, "end": **********.595212, "relative_end": **********.595212, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.carousel", "start": **********.595854, "relative_start": 0.8211219310760498, "end": **********.595854, "relative_end": **********.595854, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.cards.grid", "start": **********.596231, "relative_start": 0.8214988708496094, "end": **********.596231, "relative_end": **********.596231, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.carousel", "start": **********.6132, "relative_start": 0.8384678363800049, "end": **********.6132, "relative_end": **********.6132, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.carousel", "start": **********.613768, "relative_start": 0.8390359878540039, "end": **********.613768, "relative_end": **********.613768, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.cards.grid", "start": **********.614113, "relative_start": 0.8393809795379639, "end": **********.614113, "relative_end": **********.614113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.carousel", "start": **********.625651, "relative_start": 0.8509187698364258, "end": **********.625651, "relative_end": **********.625651, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.carousel", "start": **********.626216, "relative_start": 0.8514838218688965, "end": **********.626216, "relative_end": **********.626216, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.cards.grid", "start": **********.626563, "relative_start": 0.8518309593200684, "end": **********.626563, "relative_end": **********.626563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.index", "start": **********.643283, "relative_start": 0.8685507774353027, "end": **********.643283, "relative_end": **********.643283, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: marketplace::components.shop.layouts.header.style", "start": **********.659653, "relative_start": 0.8849208354949951, "end": **********.659653, "relative_end": **********.659653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.flash-group.index", "start": **********.740917, "relative_start": 0.9661848545074463, "end": **********.740917, "relative_end": **********.740917, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.flash-group.item", "start": **********.742482, "relative_start": 0.967749834060669, "end": **********.742482, "relative_end": **********.742482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.flash-group.item", "start": **********.743559, "relative_start": 0.9688267707824707, "end": **********.743559, "relative_end": **********.743559, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.modal.confirm", "start": **********.744571, "relative_start": 0.9698388576507568, "end": **********.744571, "relative_end": **********.744571, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.header.index", "start": **********.746052, "relative_start": 0.9713199138641357, "end": **********.746052, "relative_end": **********.746052, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.header.desktop.top", "start": **********.759418, "relative_start": 0.9846858978271484, "end": **********.759418, "relative_end": **********.759418, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.dropdown.index", "start": **********.762549, "relative_start": 0.9878168106079102, "end": **********.762549, "relative_end": **********.762549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.dropdown.index", "start": **********.809677, "relative_start": 1.034944772720337, "end": **********.809677, "relative_end": **********.809677, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.header.desktop.index", "start": **********.81616, "relative_start": 1.0414278507232666, "end": **********.81616, "relative_end": **********.81616, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.header.desktop.bottom", "start": **********.817704, "relative_start": 1.0429718494415283, "end": **********.817704, "relative_end": **********.817704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::search.images.index", "start": **********.883289, "relative_start": 1.1085569858551025, "end": **********.883289, "relative_end": **********.883289, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::checkout.cart.mini-cart", "start": **********.900488, "relative_start": 1.125755786895752, "end": **********.900488, "relative_end": **********.900488, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.quantity-changer.index", "start": **********.956154, "relative_start": 1.1814219951629639, "end": **********.956154, "relative_end": **********.956154, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.drawer.index", "start": **********.9592, "relative_start": 1.1844677925109863, "end": **********.9592, "relative_end": **********.9592, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: marketplace::components.shop.layouts.header.sell", "start": **********.99114, "relative_start": 1.2164077758789062, "end": **********.99114, "relative_end": **********.99114, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: marketplace::components.shop.layouts.header.profile", "start": **********.996678, "relative_start": 1.2219460010528564, "end": **********.996678, "relative_end": **********.996678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.dropdown.index", "start": **********.997922, "relative_start": 1.2231898307800293, "end": **********.997922, "relative_end": **********.997922, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.drawer.index", "start": **********.009593, "relative_start": 1.234860897064209, "end": **********.009593, "relative_end": **********.009593, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.header.mobile.index", "start": **********.010902, "relative_start": 1.2361698150634766, "end": **********.010902, "relative_end": **********.010902, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::checkout.cart.mini-cart", "start": **********.031339, "relative_start": 1.2566068172454834, "end": **********.031339, "relative_end": **********.031339, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: marketplace::components.shop.layouts.header.sell", "start": **********.032698, "relative_start": 1.2579658031463623, "end": **********.032698, "relative_end": **********.032698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.dropdown.index", "start": **********.040398, "relative_start": 1.2656657695770264, "end": **********.040398, "relative_end": **********.040398, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::search.images.index", "start": **********.044413, "relative_start": 1.2696809768676758, "end": **********.044413, "relative_end": **********.044413, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: marketplace::components.shop.layouts.header.profile", "start": **********.045356, "relative_start": 1.2706239223480225, "end": **********.045356, "relative_end": **********.045356, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.drawer.index", "start": **********.05048, "relative_start": 1.2757477760314941, "end": **********.05048, "relative_end": **********.05048, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.drawer.index", "start": **********.056029, "relative_start": 1.281296968460083, "end": **********.056029, "relative_end": **********.056029, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.drawer.index", "start": **********.056678, "relative_start": 1.2819459438323975, "end": **********.056678, "relative_end": **********.056678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.services", "start": **********.069702, "relative_start": 1.2949697971343994, "end": **********.069702, "relative_end": **********.069702, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.footer.index", "start": **********.091762, "relative_start": 1.3170299530029297, "end": **********.091762, "relative_end": **********.091762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.accordion.index", "start": **********.154194, "relative_start": 1.3794620037078857, "end": **********.154194, "relative_end": **********.154194, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.control", "start": **********.177344, "relative_start": 1.4026119709014893, "end": **********.177344, "relative_end": **********.177344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.error", "start": **********.180741, "relative_start": 1.4060089588165283, "end": **********.180741, "relative_end": **********.180741, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.index", "start": **********.182167, "relative_start": 1.4074349403381348, "end": **********.182167, "relative_end": **********.182167, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core::blade.tracer.style", "start": **********.18541, "relative_start": 1.4106779098510742, "end": **********.18541, "relative_end": **********.18541, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: paypal::checkout.onepage.paypal-smart-button", "start": **********.18893, "relative_start": 1.4141979217529297, "end": **********.18893, "relative_end": **********.18893, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 48751104, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 64, "nb_templates": 64, "templates": [{"name": "1x shop::home.index", "param_count": null, "params": [], "start": **********.419207, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/home/<USER>", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fhome%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::home.index"}, {"name": "1x shop::components.carousel.index", "param_count": null, "params": [], "start": **********.466845, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/carousel/index.blade.phpshop::components.carousel.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fcarousel%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.carousel.index"}, {"name": "4x shop::components.media.images.lazy", "param_count": null, "params": [], "start": **********.467836, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/media/images/lazy.blade.phpshop::components.media.images.lazy", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fmedia%2Fimages%2Flazy.blade.php&line=1", "ajax": false, "filename": "lazy.blade.php", "line": "?"}, "render_count": 4, "name_original": "shop::components.media.images.lazy"}, {"name": "1x shop::components.categories.carousel", "param_count": null, "params": [], "start": **********.483524, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/categories/carousel.blade.phpshop::components.categories.carousel", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fcategories%2Fcarousel.blade.php&line=1", "ajax": false, "filename": "carousel.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.categories.carousel"}, {"name": "2x shop::components.shimmer.categories.carousel", "param_count": null, "params": [], "start": **********.484502, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/shimmer/categories/carousel.blade.phpshop::components.shimmer.categories.carousel", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fcategories%2Fcarousel.blade.php&line=1", "ajax": false, "filename": "carousel.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::components.shimmer.categories.carousel"}, {"name": "4x shop::components.products.carousel", "param_count": null, "params": [], "start": **********.499046, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/products/carousel.blade.phpshop::components.products.carousel", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fproducts%2Fcarousel.blade.php&line=1", "ajax": false, "filename": "carousel.blade.php", "line": "?"}, "render_count": 4, "name_original": "shop::components.products.carousel"}, {"name": "5x shop::components.shimmer.products.carousel", "param_count": null, "params": [], "start": **********.500494, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/shimmer/products/carousel.blade.phpshop::components.shimmer.products.carousel", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fproducts%2Fcarousel.blade.php&line=1", "ajax": false, "filename": "carousel.blade.php", "line": "?"}, "render_count": 5, "name_original": "shop::components.shimmer.products.carousel"}, {"name": "5x shop::components.shimmer.products.cards.grid", "param_count": null, "params": [], "start": **********.501619, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/shimmer/products/cards/grid.blade.phpshop::components.shimmer.products.cards.grid", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fproducts%2Fcards%2Fgrid.blade.php&line=1", "ajax": false, "filename": "grid.blade.php", "line": "?"}, "render_count": 5, "name_original": "shop::components.shimmer.products.cards.grid"}, {"name": "1x shop::components.products.card", "param_count": null, "params": [], "start": **********.503344, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/products/card.blade.phpshop::components.products.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fproducts%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.products.card"}, {"name": "2x shop::components.products.ratings", "param_count": null, "params": [], "start": **********.514853, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/products/ratings.blade.phpshop::components.products.ratings", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fproducts%2Fratings.blade.php&line=1", "ajax": false, "filename": "ratings.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::components.products.ratings"}, {"name": "1x shop::components.button.index", "param_count": null, "params": [], "start": **********.587911, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/button/index.blade.phpshop::components.button.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.button.index"}, {"name": "1x shop::components.layouts.index", "param_count": null, "params": [], "start": **********.643223, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/index.blade.phpshop::components.layouts.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.index"}, {"name": "1x marketplace::components.shop.layouts.header.style", "param_count": null, "params": [], "start": **********.659606, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src/resources/views/components/shop/layouts/header/style.blade.phpmarketplace::components.shop.layouts.header.style", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshop%2Flayouts%2Fheader%2Fstyle.blade.php&line=1", "ajax": false, "filename": "style.blade.php", "line": "?"}, "render_count": 1, "name_original": "marketplace::components.shop.layouts.header.style"}, {"name": "1x shop::components.flash-group.index", "param_count": null, "params": [], "start": **********.740706, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/flash-group/index.blade.phpshop::components.flash-group.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fflash-group%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.flash-group.index"}, {"name": "2x shop::components.flash-group.item", "param_count": null, "params": [], "start": **********.742382, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/flash-group/item.blade.phpshop::components.flash-group.item", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fflash-group%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::components.flash-group.item"}, {"name": "1x shop::components.modal.confirm", "param_count": null, "params": [], "start": **********.744486, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/modal/confirm.blade.phpshop::components.modal.confirm", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fmodal%2Fconfirm.blade.php&line=1", "ajax": false, "filename": "confirm.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.modal.confirm"}, {"name": "1x shop::components.layouts.header.index", "param_count": null, "params": [], "start": **********.745944, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/index.blade.phpshop::components.layouts.header.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.header.index"}, {"name": "1x shop::components.layouts.header.desktop.top", "param_count": null, "params": [], "start": **********.759291, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/top.blade.phpshop::components.layouts.header.desktop.top", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Ftop.blade.php&line=1", "ajax": false, "filename": "top.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.header.desktop.top"}, {"name": "4x shop::components.dropdown.index", "param_count": null, "params": [], "start": **********.762427, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems/resources/themes/default/views/components/dropdown/index.blade.phpshop::components.dropdown.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fresources%2Fthemes%2Fdefault%2Fviews%2Fcomponents%2Fdropdown%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 4, "name_original": "shop::components.dropdown.index"}, {"name": "1x shop::components.layouts.header.desktop.index", "param_count": null, "params": [], "start": **********.816061, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/index.blade.phpshop::components.layouts.header.desktop.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.header.desktop.index"}, {"name": "1x shop::components.layouts.header.desktop.bottom", "param_count": null, "params": [], "start": **********.817605, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/bottom.blade.phpshop::components.layouts.header.desktop.bottom", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Fbottom.blade.php&line=1", "ajax": false, "filename": "bottom.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.header.desktop.bottom"}, {"name": "2x shop::search.images.index", "param_count": null, "params": [], "start": **********.883085, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/search/images/index.blade.phpshop::search.images.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fsearch%2Fimages%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::search.images.index"}, {"name": "2x shop::checkout.cart.mini-cart", "param_count": null, "params": [], "start": **********.900359, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/checkout/cart/mini-cart.blade.phpshop::checkout.cart.mini-cart", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcheckout%2Fcart%2Fmini-cart.blade.php&line=1", "ajax": false, "filename": "mini-cart.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::checkout.cart.mini-cart"}, {"name": "1x shop::components.quantity-changer.index", "param_count": null, "params": [], "start": **********.956081, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/quantity-changer/index.blade.phpshop::components.quantity-changer.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fquantity-changer%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.quantity-changer.index"}, {"name": "5x shop::components.drawer.index", "param_count": null, "params": [], "start": **********.958983, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/drawer/index.blade.phpshop::components.drawer.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fdrawer%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 5, "name_original": "shop::components.drawer.index"}, {"name": "2x marketplace::components.shop.layouts.header.sell", "param_count": null, "params": [], "start": **********.991066, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src/resources/views/components/shop/layouts/header/sell.blade.phpmarketplace::components.shop.layouts.header.sell", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshop%2Flayouts%2Fheader%2Fsell.blade.php&line=1", "ajax": false, "filename": "sell.blade.php", "line": "?"}, "render_count": 2, "name_original": "marketplace::components.shop.layouts.header.sell"}, {"name": "2x marketplace::components.shop.layouts.header.profile", "param_count": null, "params": [], "start": **********.99661, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src/resources/views/components/shop/layouts/header/profile.blade.phpmarketplace::components.shop.layouts.header.profile", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshop%2Flayouts%2Fheader%2Fprofile.blade.php&line=1", "ajax": false, "filename": "profile.blade.php", "line": "?"}, "render_count": 2, "name_original": "marketplace::components.shop.layouts.header.profile"}, {"name": "1x shop::components.layouts.header.mobile.index", "param_count": null, "params": [], "start": **********.010816, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.phpshop::components.layouts.header.mobile.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fmobile%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.header.mobile.index"}, {"name": "1x shop::components.layouts.services", "param_count": null, "params": [], "start": **********.069605, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/services.blade.phpshop::components.layouts.services", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fservices.blade.php&line=1", "ajax": false, "filename": "services.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.services"}, {"name": "1x shop::components.layouts.footer.index", "param_count": null, "params": [], "start": **********.091646, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/footer/index.blade.phpshop::components.layouts.footer.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Ffooter%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.footer.index"}, {"name": "1x shop::components.accordion.index", "param_count": null, "params": [], "start": **********.153807, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/accordion/index.blade.phpshop::components.accordion.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Faccordion%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.accordion.index"}, {"name": "1x shop::components.form.control-group.control", "param_count": null, "params": [], "start": **********.177217, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/form/control-group/control.blade.phpshop::components.form.control-group.control", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Fcontrol.blade.php&line=1", "ajax": false, "filename": "control.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.form.control-group.control"}, {"name": "1x shop::components.form.control-group.error", "param_count": null, "params": [], "start": **********.180609, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/form/control-group/error.blade.phpshop::components.form.control-group.error", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.form.control-group.error"}, {"name": "1x shop::components.form.index", "param_count": null, "params": [], "start": **********.18204, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/form/index.blade.phpshop::components.form.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.form.index"}, {"name": "1x core::blade.tracer.style", "param_count": null, "params": [], "start": **********.18525, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src/resources/views/blade/tracer/style.blade.phpcore::blade.tracer.style", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FCore%2Fsrc%2FResources%2Fviews%2Fblade%2Ftracer%2Fstyle.blade.php&line=1", "ajax": false, "filename": "style.blade.php", "line": "?"}, "render_count": 1, "name_original": "core::blade.tracer.style"}, {"name": "1x paypal::checkout.onepage.paypal-smart-button", "param_count": null, "params": [], "start": **********.188685, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Paypal\\src/resources/views/checkout/onepage/paypal-smart-button.blade.phppaypal::checkout.onepage.paypal-smart-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FPaypal%2Fsrc%2FResources%2Fviews%2Fcheckout%2Fonepage%2Fpaypal-smart-button.blade.php&line=1", "ajax": false, "filename": "paypal-smart-button.blade.php", "line": "?"}, "render_count": 1, "name_original": "paypal::checkout.onepage.paypal-smart-button"}]}, "queries": {"count": 49, "nb_statements": 49, "nb_visible_statements": 49, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.05358999999999999, "accumulated_duration_str": "53.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "installer_locale", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "theme", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Theme.php", "line": 32}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.319906, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mditems", "explain": null, "start_percent": 0, "width_percent": 1.53}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "currency", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "installer_locale", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 40}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.324388, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mditems", "explain": null, "start_percent": 1.53, "width_percent": 1.101}, {"sql": "select * from `currencies` where `code` = 'USD'", "type": "query", "params": [], "bindings": ["USD"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 296}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 295}], "start": **********.329762, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "mditems", "explain": null, "start_percent": 2.631, "width_percent": 1.586}, {"sql": "select * from `visits` where (`method` = 'GET' and `url` = 'http://127.0.0.1:8000/storage/theme/5/BvVWSMy5u18lsS3blsfAgYkwPQ1BhpsuGIAT5PF2.webp' and `ip` = '127.0.0.1' and `visitor_id` is null and `visitor_type` is null and `channel_id` = 1) order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["GET", "http://127.0.0.1:8000/storage/theme/5/BvVWSMy5u18lsS3blsfAgYkwPQ1BhpsuGIAT5PF2.webp", "127.0.0.1", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Jobs/UpdateCreateVisitIndex.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Jobs\\UpdateCreateVisitIndex.php", "line": 45}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.3787348, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "UpdateCreateVisitIndex.php:45", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Jobs/UpdateCreateVisitIndex.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Jobs\\UpdateCreateVisitIndex.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FCore%2Fsrc%2FJobs%2FUpdateCreateVisitIndex.php&line=45", "ajax": false, "filename": "UpdateCreateVisitIndex.php", "line": "45"}, "connection": "mditems", "explain": null, "start_percent": 4.217, "width_percent": 1.754}, {"sql": "insert into `visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `channel_id`, `updated_at`, `created_at`) values ('GET', '[]', 'http://127.0.0.1:8000/storage/theme/5/BvVWSMy5u18lsS3blsfAgYkwPQ1BhpsuGIAT5PF2.webp', 'http://127.0.0.1:8000/', '[]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\\\"host\\\":[\\\"127.0.0.1:8000\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"sec-ch-ua-platform\\\":[\\\"\\\\\\\"Windows\\\\\\\"\\\"],\\\"user-agent\\\":[\\\"Mozilla\\\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\\\/537.36 (KHTML, like Gecko) Chrome\\\\/********* Safari\\\\/537.36\\\"],\\\"sec-ch-ua\\\":[\\\"\\\\\\\"Not;A=Brand\\\\\\\";v=\\\\\\\"99\\\\\\\", \\\\\\\"Google Chrome\\\\\\\";v=\\\\\\\"139\\\\\\\", \\\\\\\"Chromium\\\\\\\";v=\\\\\\\"139\\\\\\\"\\\"],\\\"sec-ch-ua-mobile\\\":[\\\"?0\\\"],\\\"accept\\\":[\\\"image\\\\/avif,image\\\\/webp,image\\\\/apng,image\\\\/svg+xml,image\\\\/*,*\\\\/*;q=0.8\\\"],\\\"sec-fetch-site\\\":[\\\"same-origin\\\"],\\\"sec-fetch-mode\\\":[\\\"no-cors\\\"],\\\"sec-fetch-dest\\\":[\\\"image\\\"],\\\"referer\\\":[\\\"http:\\\\/\\\\/127.0.0.1:8000\\\\/\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate, br, zstd\\\"],\\\"accept-language\\\":[\\\"en-US,en;q=0.9\\\"],\\\"cookie\\\":[\\\"_gcl_au=1.1.1178683659.1752735529; XSRF-TOKEN=eyJpdiI6IkhyVDBhTXYxRkY4Tk5udGdOSFkzRWc9PSIsInZhbHVlIjoiMEQzU2FnVzhrdEdWenVoZi90MURsd3l2ZnVvOWpJN0hYcFhUK2xXNTJvT29wbCtvSGJscEIvd2dqenBkdVdoYWN6a1VrTFdaVWhvZDlBRno5ZnJqUU5CT29ybHYzUHA2Vm9BMXB1MGNUT21MZEsvaUJIMmxKUGdSbWpIZ3dzVVEiLCJtYWMiOiJiMzBiMjFkYjQ0OTMwYWYzZjBmYmQxNzU3NGE4MTFhNzZjMjMyZGY1ZWExNWI2NDM3MDVhZDYwNmFkY2Y4ZGRlIiwidGFnIjoiIn0%3D; bagisto_session=eyJpdiI6IjA0UnB6K2ZORUd1NTYrS1Zxd1lDU2c9PSIsInZhbHVlIjoib1ZrWVhUTjlMMUJ3enQ1eXZJWENTSlFiODczSjd3dU5LQzd6S1U4K0c3RDRhS28rYndQRnRLN09SeXBvcW9sQUF4S2pIZW0rZWZhVWM0TzNOeTh1MlhwZnE4WmhvbTY4Z1N0TjlKcUpKUWpvTUZEdThjU1ljWVlMT3pJam9TVEoiLCJtYWMiOiJmZTgyYWMzZDU1ZjU5ODViNDNkMjlkY2MwZDIwZThlNjYwZWY2MTkzMDczMTQ0ZmQyNjZkNmQ4YTkzNDU4ZDQxIiwidGFnIjoiIn0%3D\\\"]}', '', 'Windows', 'Chrome', '127.0.0.1', null, null, 1, '2025-08-19 18:24:57', '2025-08-19 18:24:57')", "type": "query", "params": [], "bindings": ["GET", "[]", "http://127.0.0.1:8000/storage/theme/5/BvVWSMy5u18lsS3blsfAgYkwPQ1BhpsuGIAT5PF2.webp", "http://127.0.0.1:8000/", "[]", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "{\"host\":[\"127.0.0.1:8000\"],\"connection\":[\"keep-alive\"],\"sec-ch-ua-platform\":[\"\\\"Windows\\\"\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"sec-ch-ua\":[\"\\\"Not;A=Brand\\\";v=\\\"99\\\", \\\"Google Chrome\\\";v=\\\"139\\\", \\\"Chromium\\\";v=\\\"139\\\"\"],\"sec-ch-ua-mobile\":[\"?0\"],\"accept\":[\"image\\/avif,image\\/webp,image\\/apng,image\\/svg+xml,image\\/*,*\\/*;q=0.8\"],\"sec-fetch-site\":[\"same-origin\"],\"sec-fetch-mode\":[\"no-cors\"],\"sec-fetch-dest\":[\"image\"],\"referer\":[\"http:\\/\\/127.0.0.1:8000\\/\"],\"accept-encoding\":[\"gzip, deflate, br, zstd\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"cookie\":[\"_gcl_au=1.1.1178683659.1752735529; XSRF-TOKEN=eyJpdiI6IkhyVDBhTXYxRkY4Tk5udGdOSFkzRWc9PSIsInZhbHVlIjoiMEQzU2FnVzhrdEdWenVoZi90MURsd3l2ZnVvOWpJN0hYcFhUK2xXNTJvT29wbCtvSGJscEIvd2dqenBkdVdoYWN6a1VrTFdaVWhvZDlBRno5ZnJqUU5CT29ybHYzUHA2Vm9BMXB1MGNUT21MZEsvaUJIMmxKUGdSbWpIZ3dzVVEiLCJtYWMiOiJiMzBiMjFkYjQ0OTMwYWYzZjBmYmQxNzU3NGE4MTFhNzZjMjMyZGY1ZWExNWI2NDM3MDVhZDYwNmFkY2Y4ZGRlIiwidGFnIjoiIn0%3D; bagisto_session=eyJpdiI6IjA0UnB6K2ZORUd1NTYrS1Zxd1lDU2c9PSIsInZhbHVlIjoib1ZrWVhUTjlMMUJ3enQ1eXZJWENTSlFiODczSjd3dU5LQzd6S1U4K0c3RDRhS28rYndQRnRLN09SeXBvcW9sQUF4S2pIZW0rZWZhVWM0TzNOeTh1MlhwZnE4WmhvbTY4Z1N0TjlKcUpKUWpvTUZEdThjU1ljWVlMT3pJam9TVEoiLCJtYWMiOiJmZTgyYWMzZDU1ZjU5ODViNDNkMjlkY2MwZDIwZThlNjYwZWY2MTkzMDczMTQ0ZmQyNjZkNmQ4YTkzNDU4ZDQxIiwidGFnIjoiIn0%3D\"]}", "", "Windows", "Chrome", "127.0.0.1", null, null, 1, "2025-08-19 18:24:57", "2025-08-19 18:24:57"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Jobs/UpdateCreateVisitIndex.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Jobs\\UpdateCreateVisitIndex.php", "line": 57}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.38349, "duration": 0.00677, "duration_str": "6.77ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:651", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=651", "ajax": false, "filename": "BaseRepository.php", "line": "651"}, "connection": "mditems", "explain": null, "start_percent": 5.971, "width_percent": 12.633}, {"sql": "select * from `theme_customizations` where `status` = 1 and `channel_id` = 1 order by `sort_order` asc", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Marketplace/src/Http/Controllers/Shop/ProductsCategoriesProxyController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Http\\Controllers\\Shop\\ProductsCategoriesProxyController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.399683, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 18.604, "width_percent": 2.687}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 22, "namespace": null, "name": "packages/Webkul/Marketplace/src/Http/Controllers/Shop/ProductsCategoriesProxyController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Http\\Controllers\\Shop\\ProductsCategoriesProxyController.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.4093308, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 21.291, "width_percent": 1.064}, {"sql": "select * from `locales`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, {"index": 17, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 218}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 217}], "start": **********.4230912, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:346", "source": {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=346", "ajax": false, "filename": "BaseRepository.php", "line": "346"}, "connection": "mditems", "explain": null, "start_percent": 22.355, "width_percent": 0.877}, {"sql": "select * from `channel_translations` where `channel_translations`.`channel_id` = 1 and `channel_translations`.`channel_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": "view", "name": "shop::home.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/home/<USER>", "line": 9}], "start": **********.434502, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mditems", "explain": null, "start_percent": 23.232, "width_percent": 1.661}, {"sql": "select * from `core_config` where `code` = 'catalog.products.review.summary'", "type": "query", "params": [], "bindings": ["catalog.products.review.summary"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.508949, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 24.893, "width_percent": 1.53}, {"sql": "select * from `core_config` where `code` = 'customer.settings.wishlist.wishlist_option'", "type": "query", "params": [], "bindings": ["customer.settings.wishlist.wishlist_option"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.520974, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 26.423, "width_percent": 1.623}, {"sql": "select * from `core_config` where `code` = 'catalog.products.settings.compare_option'", "type": "query", "params": [], "bindings": ["catalog.products.settings.compare_option"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.534054, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 28.046, "width_percent": 1.4}, {"sql": "select * from `core_config` where `code` = 'sales.checkout.shopping_cart.cart_page'", "type": "query", "params": [], "bindings": ["sales.checkout.shopping_cart.cart_page"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.547594, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 29.446, "width_percent": 1.493}, {"sql": "select * from `locales` where `code` = 'en'", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 296}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 295}], "start": **********.649085, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "mditems", "explain": null, "start_percent": 30.939, "width_percent": 1.922}, {"sql": "select * from `core_config` where `code` = 'general.content.custom_scripts.custom_css' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["general.content.custom_scripts.custom_css", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.6781192, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 32.861, "width_percent": 1.493}, {"sql": "select * from `core_config` where `code` = 'general.content.speculation_rules.enabled'", "type": "query", "params": [], "bindings": ["general.content.speculation_rules.enabled"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.687454, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 34.353, "width_percent": 1.269}, {"sql": "select * from `core_config` where `code` = 'general.content.speculation_rules.prerender_enabled'", "type": "query", "params": [], "bindings": ["general.content.speculation_rules.prerender_enabled"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.6962202, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 35.622, "width_percent": 1.194}, {"sql": "select * from `core_config` where `code` = 'general.content.speculation_rules.prerender_eagerness'", "type": "query", "params": [], "bindings": ["general.content.speculation_rules.prerender_eagerness"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.702703, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 36.817, "width_percent": 2.277}, {"sql": "select * from `core_config` where `code` = 'general.content.speculation_rules.prerender_ignore_urls'", "type": "query", "params": [], "bindings": ["general.content.speculation_rules.prerender_ignore_urls"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.711664, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 39.093, "width_percent": 1.437}, {"sql": "select * from `core_config` where `code` = 'general.content.speculation_rules.prerender_ignore_url_params'", "type": "query", "params": [], "bindings": ["general.content.speculation_rules.prerender_ignore_url_params"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.721057, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 40.53, "width_percent": 1.661}, {"sql": "select * from `core_config` where `code` = 'general.content.speculation_rules.prefetch_enabled'", "type": "query", "params": [], "bindings": ["general.content.speculation_rules.prefetch_enabled"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.731607, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 42.191, "width_percent": 1.4}, {"sql": "select count(*) as aggregate from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "shop::components.layouts.header.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/index.blade.php", "line": 4}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.747187, "duration": 0.0032, "duration_str": "3.2ms", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.index:4", "source": {"index": 19, "namespace": "view", "name": "shop::components.layouts.header.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/index.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Findex.blade.php&line=4", "ajax": false, "filename": "index.blade.php", "line": "4"}, "connection": "mditems", "explain": null, "start_percent": 43.59, "width_percent": 5.971}, {"sql": "select count(*) as aggregate from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "shop::components.layouts.header.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/index.blade.php", "line": 4}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.7540252, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.index:4", "source": {"index": 19, "namespace": "view", "name": "shop::components.layouts.header.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/index.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Findex.blade.php&line=4", "ajax": false, "filename": "index.blade.php", "line": "4"}, "connection": "mditems", "explain": null, "start_percent": 49.561, "width_percent": 2.463}, {"sql": "select * from `core_config` where `code` = 'general.content.header_offer.title'", "type": "query", "params": [], "bindings": ["general.content.header_offer.title"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.7686992, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 52.025, "width_percent": 1.511}, {"sql": "select * from `core_config` where `code` = 'general.content.header_offer.redirection_link'", "type": "query", "params": [], "bindings": ["general.content.header_offer.redirection_link"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.7807329, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 53.536, "width_percent": 1.698}, {"sql": "select * from `core_config` where `code` = 'general.content.header_offer.redirection_title'", "type": "query", "params": [], "bindings": ["general.content.header_offer.redirection_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.7935889, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 55.234, "width_percent": 1.698}, {"sql": "select `name` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 and `code` = 'en' order by `name` asc limit 1", "type": "query", "params": [], "bindings": [1, "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "shop::components.layouts.header.desktop.top", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/top.blade.php", "line": 162}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.8043308, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.desktop.top:162", "source": {"index": 20, "namespace": "view", "name": "shop::components.layouts.header.desktop.top", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/top.blade.php", "line": 162}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Ftop.blade.php&line=162", "ajax": false, "filename": "top.blade.php", "line": "162"}, "connection": "mditems", "explain": null, "start_percent": 56.932, "width_percent": 2.09}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 order by `name` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "shop::components.layouts.header.desktop.top", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/top.blade.php", "line": 270}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.811938, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.desktop.top:270", "source": {"index": 15, "namespace": "view", "name": "shop::components.layouts.header.desktop.top", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/top.blade.php", "line": 270}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Ftop.blade.php&line=270", "ajax": false, "filename": "top.blade.php", "line": "270"}, "connection": "mditems", "explain": null, "start_percent": 59.022, "width_percent": 1.344}, {"sql": "select * from `attributes` where `attributes`.`id` = 33 limit 1", "type": "query", "params": [], "bindings": [33], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "shop::components.layouts.header.desktop.bottom", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/bottom.blade.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.8208182, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.desktop.bottom:44", "source": {"index": 20, "namespace": "view", "name": "shop::components.layouts.header.desktop.bottom", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/bottom.blade.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Fbottom.blade.php&line=44", "ajax": false, "filename": "bottom.blade.php", "line": "44"}, "connection": "mditems", "explain": null, "start_percent": 60.366, "width_percent": 3.788}, {"sql": "select * from `attribute_options` where `attribute_id` = 33", "type": "query", "params": [], "bindings": [33], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "shop::components.layouts.header.desktop.bottom", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/bottom.blade.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.826924, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.desktop.bottom:47", "source": {"index": 15, "namespace": "view", "name": "shop::components.layouts.header.desktop.bottom", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/bottom.blade.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Fbottom.blade.php&line=47", "ajax": false, "filename": "bottom.blade.php", "line": "47"}, "connection": "mditems", "explain": null, "start_percent": 64.154, "width_percent": 2.967}, {"sql": "select * from `core_config` where `code` = 'catalog.products.search.min_query_length'", "type": "query", "params": [], "bindings": ["catalog.products.search.min_query_length"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.844778, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 67.121, "width_percent": 2.015}, {"sql": "select * from `core_config` where `code` = 'catalog.products.search.max_query_length'", "type": "query", "params": [], "bindings": ["catalog.products.search.max_query_length"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.859206, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 69.136, "width_percent": 1.437}, {"sql": "select * from `core_config` where `code` = 'catalog.products.settings.image_search'", "type": "query", "params": [], "bindings": ["catalog.products.settings.image_search"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.873667, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 70.573, "width_percent": 1.922}, {"sql": "select * from `core_config` where `code` = 'sales.checkout.mini_cart.display_mini_cart'", "type": "query", "params": [], "bindings": ["sales.checkout.mini_cart.display_mini_cart"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.908796, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 72.495, "width_percent": 1.829}, {"sql": "select * from `core_config` where `code` = 'sales.checkout.my_cart.summary'", "type": "query", "params": [], "bindings": ["sales.checkout.my_cart.summary"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.928744, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 74.324, "width_percent": 1.567}, {"sql": "select * from `core_config` where `code` = 'sales.checkout.mini_cart.offer_info'", "type": "query", "params": [], "bindings": ["sales.checkout.mini_cart.offer_info"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.944863, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 75.891, "width_percent": 1.81}, {"sql": "select * from `core_config` where `code` = 'sales.taxes.shopping_cart.display_prices'", "type": "query", "params": [], "bindings": ["sales.taxes.shopping_cart.display_prices"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.968522, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 77.701, "width_percent": 2.93}, {"sql": "select * from `core_config` where `code` = 'sales.taxes.shopping_cart.display_subtotal'", "type": "query", "params": [], "bindings": ["sales.taxes.shopping_cart.display_subtotal"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.9811192, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 80.631, "width_percent": 1.885}, {"sql": "select * from `core_config` where `code` = 'general.design.categories.category_view'", "type": "query", "params": [], "bindings": ["general.design.categories.category_view"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.002516, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 82.515, "width_percent": 3.154}, {"sql": "select count(*) as aggregate from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "shop::components.layouts.header.mobile.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.php", "line": 359}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.045964, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.mobile.index:359", "source": {"index": 19, "namespace": "view", "name": "shop::components.layouts.header.mobile.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.php", "line": 359}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fmobile%2Findex.blade.php&line=359", "ajax": false, "filename": "index.blade.php", "line": "359"}, "connection": "mditems", "explain": null, "start_percent": 85.669, "width_percent": 1.157}, {"sql": "select count(*) as aggregate from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "shop::components.layouts.header.mobile.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.php", "line": 359}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.0484748, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.mobile.index:359", "source": {"index": 19, "namespace": "view", "name": "shop::components.layouts.header.mobile.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.php", "line": 359}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fmobile%2Findex.blade.php&line=359", "ajax": false, "filename": "index.blade.php", "line": "359"}, "connection": "mditems", "explain": null, "start_percent": 86.826, "width_percent": 0.784}, {"sql": "select `name` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 and `code` = 'en' order by `name` asc limit 1", "type": "query", "params": [], "bindings": [1, "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "shop::components.layouts.header.mobile.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.php", "line": 442}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.051286, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.mobile.index:442", "source": {"index": 20, "namespace": "view", "name": "shop::components.layouts.header.mobile.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.php", "line": 442}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fmobile%2Findex.blade.php&line=442", "ajax": false, "filename": "index.blade.php", "line": "442"}, "connection": "mditems", "explain": null, "start_percent": 87.61, "width_percent": 1.064}, {"sql": "select * from `core_config` where `code` = 'general.gdpr.settings.enabled' and `channel_code` = 'default' and `locale_code` = 'en'", "type": "query", "params": [], "bindings": ["general.gdpr.settings.enabled", "default", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.060371, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 88.673, "width_percent": 1.325}, {"sql": "select * from `theme_customizations` where `type` = 'services_content' and `status` = 1 and `theme_code` = 'default' and `channel_id` = 1", "type": "query", "params": [], "bindings": ["services_content", 1, "default", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": "view", "name": "shop::components.layouts.services", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/services.blade.php", "line": 13}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.0718071, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 89.998, "width_percent": 3.191}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (12)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 23, "namespace": "view", "name": "shop::components.layouts.services", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/services.blade.php", "line": 13}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.076853, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 93.189, "width_percent": 1.157}, {"sql": "select * from `theme_customizations` where `type` = 'footer_links' and `status` = 1 and `theme_code` = 'default' and `channel_id` = 1", "type": "query", "params": [], "bindings": ["footer_links", 1, "default", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": "view", "name": "shop::components.layouts.footer.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/footer/index.blade.php", "line": 17}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.093427, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 94.346, "width_percent": 1.567}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 23, "namespace": "view", "name": "shop::components.layouts.footer.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/footer/index.blade.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.097372, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 95.913, "width_percent": 0.952}, {"sql": "select * from `core_config` where `code` = 'customer.settings.newsletter.subscription'", "type": "query", "params": [], "bindings": ["customer.settings.newsletter.subscription"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.165034, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 96.865, "width_percent": 1.586}, {"sql": "select * from `core_config` where `code` = 'general.content.custom_scripts.custom_javascript' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["general.content.custom_scripts.custom_javascript", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.195558, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 98.451, "width_percent": 1.549}]}, "models": {"data": {"Webkul\\Theme\\Models\\ThemeCustomization": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FTheme%2Fsrc%2FModels%2FThemeCustomization.php&line=1", "ajax": false, "filename": "ThemeCustomization.php", "line": "?"}}, "Webkul\\Theme\\Models\\ThemeCustomizationTranslation": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FTheme%2Fsrc%2FModels%2FThemeCustomizationTranslation.php&line=1", "ajax": false, "filename": "ThemeCustomizationTranslation.php", "line": "?"}}, "Webkul\\Core\\Models\\Locale": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Core\\Models\\Currency": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeOption": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeOption.php&line=1", "ajax": false, "filename": "AttributeOption.php", "line": "?"}}, "Webkul\\Core\\Models\\ChannelTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannelTranslation.php&line=1", "ajax": false, "filename": "ChannelTranslation.php", "line": "?"}}, "Webkul\\Attribute\\Models\\Attribute": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "Webkul\\Core\\Models\\CoreConfig": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCoreConfig.php&line=1", "ajax": false, "filename": "CoreConfig.php", "line": "?"}}}, "count": 45, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/storage/theme/5/BvVWSMy5u18lsS3blsfAgYkwPQ1BhpsuGIAT5PF2.webp", "action_name": "shop.product_or_category.index", "controller_action": "Webkul\\Marketplace\\Http\\Controllers\\Shop\\ProductsCategoriesProxyController@index", "uri": "GET {fallbackPlaceholder}", "controller": "Webkul\\Marketplace\\Http\\Controllers\\Shop\\ProductsCategoriesProxyController@index<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FHttp%2FControllers%2FShop%2FProductsCategoriesProxyController.php&line=47\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FHttp%2FControllers%2FShop%2FProductsCategoriesProxyController.php&line=47\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Marketplace/src/Http/Controllers/Shop/ProductsCategoriesProxyController.php:47-156</a>", "middleware": "web, theme, locale, currency, marketplace, cache.response", "duration": "1.49s", "peak_memory": "48MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-620186708 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-620186708\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1668777921 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1668777921\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-821749734 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"748 characters\">_gcl_au=1.1.1178683659.1752735529; XSRF-TOKEN=eyJpdiI6IkhyVDBhTXYxRkY4Tk5udGdOSFkzRWc9PSIsInZhbHVlIjoiMEQzU2FnVzhrdEdWenVoZi90MURsd3l2ZnVvOWpJN0hYcFhUK2xXNTJvT29wbCtvSGJscEIvd2dqenBkdVdoYWN6a1VrTFdaVWhvZDlBRno5ZnJqUU5CT29ybHYzUHA2Vm9BMXB1MGNUT21MZEsvaUJIMmxKUGdSbWpIZ3dzVVEiLCJtYWMiOiJiMzBiMjFkYjQ0OTMwYWYzZjBmYmQxNzU3NGE4MTFhNzZjMjMyZGY1ZWExNWI2NDM3MDVhZDYwNmFkY2Y4ZGRlIiwidGFnIjoiIn0%3D; bagisto_session=eyJpdiI6IjA0UnB6K2ZORUd1NTYrS1Zxd1lDU2c9PSIsInZhbHVlIjoib1ZrWVhUTjlMMUJ3enQ1eXZJWENTSlFiODczSjd3dU5LQzd6S1U4K0c3RDRhS28rYndQRnRLN09SeXBvcW9sQUF4S2pIZW0rZWZhVWM0TzNOeTh1MlhwZnE4WmhvbTY4Z1N0TjlKcUpKUWpvTUZEdThjU1ljWVlMT3pJam9TVEoiLCJtYWMiOiJmZTgyYWMzZDU1ZjU5ODViNDNkMjlkY2MwZDIwZThlNjYwZWY2MTkzMDczMTQ0ZmQyNjZkNmQ4YTkzNDU4ZDQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-821749734\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-732257380 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_gcl_au</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zkGTTvhSKLJlVdv7c7khny3gz8R0QPgdNfWirEBi</span>\"\n  \"<span class=sf-dump-key>bagisto_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">d6NyqSDpTQRmraq0TUwWTC4Y0P4XwPCUN8xQsY9a</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-732257380\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-864266551 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 12:54:57 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-864266551\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-609554210 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zkGTTvhSKLJlVdv7c7khny3gz8R0QPgdNfWirEBi</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"84 characters\">http://127.0.0.1:8000/storage/channel/1/ghBTCHgGKvmgiRQ6BP8w0XynTgOkkSQiXx69toiA.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-609554210\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/storage/theme/5/BvVWSMy5u18lsS3blsfAgYkwPQ1BhpsuGIAT5PF2.webp", "action_name": "shop.product_or_category.index", "controller_action": "Webkul\\Marketplace\\Http\\Controllers\\Shop\\ProductsCategoriesProxyController@index"}, "badge": null}}