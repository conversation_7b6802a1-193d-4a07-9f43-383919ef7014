{"__meta": {"id": "01K3162WHE6W58FAY535YKJB05", "datetime": "2025-08-19 18:20:22", "utime": **********.89512, "method": "GET", "uri": "/storage/channel/1/ghBTCHgGKvmgiRQ6BP8w0XynTgOkkSQiXx69toiA.png", "ip": "127.0.0.1"}, "modules": {"count": 4, "modules": [{"name": "Webkul\\Attribute", "models": ["Webkul\\Attribute\\Models\\Attribute (1)", "Webkul\\Attribute\\Models\\AttributeOption (3)"], "views": [], "queries": [{"sql": "select * from `attributes` where `attributes`.`id` = 33 limit 1", "duration": 0.49, "duration_str": "490ms", "connection": "mditems"}, {"sql": "select * from `attribute_options` where `attribute_id` = 33", "duration": 0.46, "duration_str": "460ms", "connection": "mditems"}]}, {"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\Locale (4)", "Webkul\\Core\\Models\\Currency (2)", "Webkul\\Core\\Models\\ChannelTranslation (1)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('127.0.0.1:8000', 'http://127.0.0.1:8000', 'https://127.0.0.1:8000')", "duration": 0.57, "duration_str": "570ms", "connection": "mditems"}, {"sql": "select * from `channels` limit 1", "duration": 0.58, "duration_str": "580ms", "connection": "mditems"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.67, "duration_str": "670ms", "connection": "mditems"}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "duration": 0.67, "duration_str": "670ms", "connection": "mditems"}, {"sql": "select count(*) as aggregate from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.59, "duration_str": "590ms", "connection": "mditems"}, {"sql": "select count(*) as aggregate from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "duration": 0.42, "duration_str": "420ms", "connection": "mditems"}, {"sql": "select `name` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 and `code` = 'en' order by `name` asc limit 1", "duration": 0.74, "duration_str": "740ms", "connection": "mditems"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 order by `name` asc", "duration": 0.5, "duration_str": "500ms", "connection": "mditems"}, {"sql": "select count(*) as aggregate from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.52, "duration_str": "520ms", "connection": "mditems"}, {"sql": "select count(*) as aggregate from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "duration": 0.58, "duration_str": "580ms", "connection": "mditems"}, {"sql": "select `name` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 and `code` = 'en' order by `name` asc limit 1", "duration": 0.79, "duration_str": "790ms", "connection": "mditems"}]}, {"name": "Webkul\\Theme", "models": ["Webkul\\Theme\\Models\\ThemeCustomization (15)", "Webkul\\Theme\\Models\\ThemeCustomizationTranslation (15)"], "views": [], "queries": [{"sql": "select * from `theme_customizations` where `status` = 1 and `channel_id` = 1 order by `sort_order` asc", "duration": 1.03, "duration_str": "1.03s", "connection": "mditems"}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14)", "duration": 0.97, "duration_str": "970ms", "connection": "mditems"}, {"sql": "select * from `theme_customizations` where `type` = 'services_content' and `status` = 1 and `theme_code` = 'default' and `channel_id` = 1", "duration": 0.64, "duration_str": "640ms", "connection": "mditems"}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (12)", "duration": 1.01, "duration_str": "1.01s", "connection": "mditems"}, {"sql": "select * from `theme_customizations` where `type` = 'footer_links' and `status` = 1 and `theme_code` = 'default' and `channel_id` = 1", "duration": 0.9, "duration_str": "900ms", "connection": "mditems"}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (11)", "duration": 0.58, "duration_str": "580ms", "connection": "mditems"}]}, {"name": "Webkul\\Marketplace", "models": ["Webkul\\Marketplace\\Models\\Core\\Visit (1)"], "views": [], "queries": [{"sql": "select * from `visits` where (`method` = 'GET' and `url` = 'http://127.0.0.1:8000/storage/channel/1/ghBTCHgGKvmgiRQ6BP8w0XynTgOkkSQiXx69toiA.png' and `ip` = '127.0.0.1' and `visitor_id` is null and `visitor_type` is null and `channel_id` = 1) order by `created_at` desc limit 1", "duration": 1.52, "duration_str": "1.52s", "connection": "mditems"}]}]}, "messages": {"count": 1, "messages": [{"message": "[18:20:22] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Core.php on line 961", "message_html": null, "is_string": false, "label": "warning", "time": **********.766229, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.32668, "end": **********.905297, "duration": 0.5786170959472656, "duration_str": "579ms", "measures": [{"label": "Booting", "start": **********.32668, "relative_start": 0, "end": **********.566151, "relative_end": **********.566151, "duration": 0.****************, "duration_str": "239ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.566161, "relative_start": 0.*****************, "end": **********.905299, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "339ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.578965, "relative_start": 0.****************, "end": **********.583112, "relative_end": **********.583112, "duration": 0.004147052764892578, "duration_str": "4.15ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.636416, "relative_start": 0.****************, "end": **********.892674, "relative_end": **********.892674, "duration": 0.****************, "duration_str": "256ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: shop::home.index", "start": **********.638379, "relative_start": 0.*****************, "end": **********.638379, "relative_end": **********.638379, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.carousel.index", "start": **********.663238, "relative_start": 0.*****************, "end": **********.663238, "relative_end": **********.663238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.media.images.lazy", "start": **********.66382, "relative_start": 0.3371400833129883, "end": **********.66382, "relative_end": **********.66382, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.categories.carousel", "start": **********.674203, "relative_start": 0.3475229740142822, "end": **********.674203, "relative_end": **********.674203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.categories.carousel", "start": **********.67479, "relative_start": 0.3481099605560303, "end": **********.67479, "relative_end": **********.67479, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.media.images.lazy", "start": **********.676064, "relative_start": 0.349384069442749, "end": **********.676064, "relative_end": **********.676064, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.categories.carousel", "start": **********.676353, "relative_start": 0.3496730327606201, "end": **********.676353, "relative_end": **********.676353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.carousel", "start": **********.681369, "relative_start": 0.3546891212463379, "end": **********.681369, "relative_end": **********.681369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.carousel", "start": **********.681933, "relative_start": 0.3552529811859131, "end": **********.681933, "relative_end": **********.681933, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.cards.grid", "start": **********.682344, "relative_start": 0.3556640148162842, "end": **********.682344, "relative_end": **********.682344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.card", "start": **********.68279, "relative_start": 0.3561100959777832, "end": **********.68279, "relative_end": **********.68279, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.media.images.lazy", "start": **********.683552, "relative_start": 0.3568720817565918, "end": **********.683552, "relative_end": **********.683552, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.ratings", "start": **********.686459, "relative_start": 0.35977911949157715, "end": **********.686459, "relative_end": **********.686459, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.media.images.lazy", "start": **********.699532, "relative_start": 0.***************, "end": **********.699532, "relative_end": **********.699532, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.ratings", "start": **********.705868, "relative_start": 0.37918806076049805, "end": **********.705868, "relative_end": **********.705868, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.button.index", "start": **********.708374, "relative_start": 0.38169407844543457, "end": **********.708374, "relative_end": **********.708374, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.carousel", "start": **********.708857, "relative_start": 0.38217711448669434, "end": **********.708857, "relative_end": **********.708857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.cards.grid", "start": **********.709121, "relative_start": 0.38244104385375977, "end": **********.709121, "relative_end": **********.709121, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.carousel", "start": **********.713216, "relative_start": 0.3865361213684082, "end": **********.713216, "relative_end": **********.713216, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.carousel", "start": **********.713639, "relative_start": 0.3869590759277344, "end": **********.713639, "relative_end": **********.713639, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.cards.grid", "start": **********.713891, "relative_start": 0.3872110843658447, "end": **********.713891, "relative_end": **********.713891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.carousel", "start": **********.725982, "relative_start": 0.39930200576782227, "end": **********.725982, "relative_end": **********.725982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.carousel", "start": **********.726397, "relative_start": 0.3997170925140381, "end": **********.726397, "relative_end": **********.726397, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.cards.grid", "start": **********.726645, "relative_start": 0.3999650478363037, "end": **********.726645, "relative_end": **********.726645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.carousel", "start": **********.734048, "relative_start": 0.****************, "end": **********.734048, "relative_end": **********.734048, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.carousel", "start": **********.73465, "relative_start": 0.*****************, "end": **********.73465, "relative_end": **********.73465, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.cards.grid", "start": **********.73513, "relative_start": 0.****************, "end": **********.73513, "relative_end": **********.73513, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.index", "start": **********.745999, "relative_start": 0.*****************, "end": **********.745999, "relative_end": **********.745999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: marketplace::components.shop.layouts.header.style", "start": **********.748413, "relative_start": 0.*****************, "end": **********.748413, "relative_end": **********.748413, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.flash-group.index", "start": **********.768951, "relative_start": 0.44227099418640137, "end": **********.768951, "relative_end": **********.768951, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.flash-group.item", "start": **********.769508, "relative_start": 0.4428279399871826, "end": **********.769508, "relative_end": **********.769508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.flash-group.item", "start": **********.769811, "relative_start": 0.4431309700012207, "end": **********.769811, "relative_end": **********.769811, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.modal.confirm", "start": **********.770186, "relative_start": 0.44350600242614746, "end": **********.770186, "relative_end": **********.770186, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.header.index", "start": **********.770659, "relative_start": 0.44397902488708496, "end": **********.770659, "relative_end": **********.770659, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.header.desktop.top", "start": **********.775435, "relative_start": 0.44875502586364746, "end": **********.775435, "relative_end": **********.775435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.dropdown.index", "start": **********.776116, "relative_start": 0.4494359493255615, "end": **********.776116, "relative_end": **********.776116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.dropdown.index", "start": **********.784595, "relative_start": 0.4579150676727295, "end": **********.784595, "relative_end": **********.784595, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.header.desktop.index", "start": **********.787872, "relative_start": 0.46119213104248047, "end": **********.787872, "relative_end": **********.787872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.header.desktop.bottom", "start": **********.788323, "relative_start": 0.4616429805755615, "end": **********.788323, "relative_end": **********.788323, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::search.images.index", "start": **********.801352, "relative_start": 0.4746720790863037, "end": **********.801352, "relative_end": **********.801352, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::checkout.cart.mini-cart", "start": **********.807784, "relative_start": 0.48110413551330566, "end": **********.807784, "relative_end": **********.807784, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.quantity-changer.index", "start": **********.814971, "relative_start": 0.48829102516174316, "end": **********.814971, "relative_end": **********.814971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.drawer.index", "start": **********.815751, "relative_start": 0.4890711307525635, "end": **********.815751, "relative_end": **********.815751, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: marketplace::components.shop.layouts.header.sell", "start": **********.820177, "relative_start": 0.4934971332550049, "end": **********.820177, "relative_end": **********.820177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: marketplace::components.shop.layouts.header.profile", "start": **********.822674, "relative_start": 0.49599409103393555, "end": **********.822674, "relative_end": **********.822674, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.dropdown.index", "start": **********.823337, "relative_start": 0.496657133102417, "end": **********.823337, "relative_end": **********.823337, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.drawer.index", "start": **********.825633, "relative_start": 0.49895310401916504, "end": **********.825633, "relative_end": **********.825633, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.header.mobile.index", "start": **********.826212, "relative_start": 0.49953198432922363, "end": **********.826212, "relative_end": **********.826212, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::checkout.cart.mini-cart", "start": **********.833661, "relative_start": 0.5069811344146729, "end": **********.833661, "relative_end": **********.833661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: marketplace::components.shop.layouts.header.sell", "start": **********.834222, "relative_start": 0.5075421333312988, "end": **********.834222, "relative_end": **********.834222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.dropdown.index", "start": **********.836507, "relative_start": 0.5098271369934082, "end": **********.836507, "relative_end": **********.836507, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::search.images.index", "start": **********.83884, "relative_start": 0.512160062789917, "end": **********.83884, "relative_end": **********.83884, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: marketplace::components.shop.layouts.header.profile", "start": **********.839499, "relative_start": 0.5128190517425537, "end": **********.839499, "relative_end": **********.839499, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.drawer.index", "start": **********.843507, "relative_start": 0.5168271064758301, "end": **********.843507, "relative_end": **********.843507, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.drawer.index", "start": **********.848363, "relative_start": 0.5216829776763916, "end": **********.848363, "relative_end": **********.848363, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.drawer.index", "start": **********.84898, "relative_start": 0.5223000049591064, "end": **********.84898, "relative_end": **********.84898, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.services", "start": **********.852735, "relative_start": 0.526055097579956, "end": **********.852735, "relative_end": **********.852735, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.footer.index", "start": **********.863544, "relative_start": 0.5368640422821045, "end": **********.863544, "relative_end": **********.863544, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.accordion.index", "start": **********.882923, "relative_start": 0.5562429428100586, "end": **********.882923, "relative_end": **********.882923, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.control", "start": **********.887825, "relative_start": 0.5611450672149658, "end": **********.887825, "relative_end": **********.887825, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.error", "start": **********.888651, "relative_start": 0.5619709491729736, "end": **********.888651, "relative_end": **********.888651, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.index", "start": **********.889015, "relative_start": 0.5623350143432617, "end": **********.889015, "relative_end": **********.889015, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core::blade.tracer.style", "start": **********.889629, "relative_start": 0.5629489421844482, "end": **********.889629, "relative_end": **********.889629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: paypal::checkout.onepage.paypal-smart-button", "start": **********.889965, "relative_start": 0.5632851123809814, "end": **********.889965, "relative_end": **********.889965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 48572984, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 64, "nb_templates": 64, "templates": [{"name": "1x shop::home.index", "param_count": null, "params": [], "start": **********.638336, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/home/<USER>", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fhome%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::home.index"}, {"name": "1x shop::components.carousel.index", "param_count": null, "params": [], "start": **********.663197, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/carousel/index.blade.phpshop::components.carousel.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fcarousel%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.carousel.index"}, {"name": "4x shop::components.media.images.lazy", "param_count": null, "params": [], "start": **********.663781, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/media/images/lazy.blade.phpshop::components.media.images.lazy", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fmedia%2Fimages%2Flazy.blade.php&line=1", "ajax": false, "filename": "lazy.blade.php", "line": "?"}, "render_count": 4, "name_original": "shop::components.media.images.lazy"}, {"name": "1x shop::components.categories.carousel", "param_count": null, "params": [], "start": **********.674153, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/categories/carousel.blade.phpshop::components.categories.carousel", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fcategories%2Fcarousel.blade.php&line=1", "ajax": false, "filename": "carousel.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.categories.carousel"}, {"name": "2x shop::components.shimmer.categories.carousel", "param_count": null, "params": [], "start": **********.674752, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/shimmer/categories/carousel.blade.phpshop::components.shimmer.categories.carousel", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fcategories%2Fcarousel.blade.php&line=1", "ajax": false, "filename": "carousel.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::components.shimmer.categories.carousel"}, {"name": "4x shop::components.products.carousel", "param_count": null, "params": [], "start": **********.68133, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/products/carousel.blade.phpshop::components.products.carousel", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fproducts%2Fcarousel.blade.php&line=1", "ajax": false, "filename": "carousel.blade.php", "line": "?"}, "render_count": 4, "name_original": "shop::components.products.carousel"}, {"name": "5x shop::components.shimmer.products.carousel", "param_count": null, "params": [], "start": **********.681895, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/shimmer/products/carousel.blade.phpshop::components.shimmer.products.carousel", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fproducts%2Fcarousel.blade.php&line=1", "ajax": false, "filename": "carousel.blade.php", "line": "?"}, "render_count": 5, "name_original": "shop::components.shimmer.products.carousel"}, {"name": "5x shop::components.shimmer.products.cards.grid", "param_count": null, "params": [], "start": **********.682301, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/shimmer/products/cards/grid.blade.phpshop::components.shimmer.products.cards.grid", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fproducts%2Fcards%2Fgrid.blade.php&line=1", "ajax": false, "filename": "grid.blade.php", "line": "?"}, "render_count": 5, "name_original": "shop::components.shimmer.products.cards.grid"}, {"name": "1x shop::components.products.card", "param_count": null, "params": [], "start": **********.682753, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/products/card.blade.phpshop::components.products.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fproducts%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.products.card"}, {"name": "2x shop::components.products.ratings", "param_count": null, "params": [], "start": **********.686414, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/products/ratings.blade.phpshop::components.products.ratings", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fproducts%2Fratings.blade.php&line=1", "ajax": false, "filename": "ratings.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::components.products.ratings"}, {"name": "1x shop::components.button.index", "param_count": null, "params": [], "start": **********.708335, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/button/index.blade.phpshop::components.button.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.button.index"}, {"name": "1x shop::components.layouts.index", "param_count": null, "params": [], "start": **********.745958, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/index.blade.phpshop::components.layouts.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.index"}, {"name": "1x marketplace::components.shop.layouts.header.style", "param_count": null, "params": [], "start": **********.748371, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src/resources/views/components/shop/layouts/header/style.blade.phpmarketplace::components.shop.layouts.header.style", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshop%2Flayouts%2Fheader%2Fstyle.blade.php&line=1", "ajax": false, "filename": "style.blade.php", "line": "?"}, "render_count": 1, "name_original": "marketplace::components.shop.layouts.header.style"}, {"name": "1x shop::components.flash-group.index", "param_count": null, "params": [], "start": **********.768907, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/flash-group/index.blade.phpshop::components.flash-group.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fflash-group%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.flash-group.index"}, {"name": "2x shop::components.flash-group.item", "param_count": null, "params": [], "start": **********.769466, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/flash-group/item.blade.phpshop::components.flash-group.item", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fflash-group%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::components.flash-group.item"}, {"name": "1x shop::components.modal.confirm", "param_count": null, "params": [], "start": **********.770144, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/modal/confirm.blade.phpshop::components.modal.confirm", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fmodal%2Fconfirm.blade.php&line=1", "ajax": false, "filename": "confirm.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.modal.confirm"}, {"name": "1x shop::components.layouts.header.index", "param_count": null, "params": [], "start": **********.770618, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/index.blade.phpshop::components.layouts.header.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.header.index"}, {"name": "1x shop::components.layouts.header.desktop.top", "param_count": null, "params": [], "start": **********.775396, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/top.blade.phpshop::components.layouts.header.desktop.top", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Ftop.blade.php&line=1", "ajax": false, "filename": "top.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.header.desktop.top"}, {"name": "4x shop::components.dropdown.index", "param_count": null, "params": [], "start": **********.776078, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems/resources/themes/default/views/components/dropdown/index.blade.phpshop::components.dropdown.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fresources%2Fthemes%2Fdefault%2Fviews%2Fcomponents%2Fdropdown%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 4, "name_original": "shop::components.dropdown.index"}, {"name": "1x shop::components.layouts.header.desktop.index", "param_count": null, "params": [], "start": **********.787833, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/index.blade.phpshop::components.layouts.header.desktop.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.header.desktop.index"}, {"name": "1x shop::components.layouts.header.desktop.bottom", "param_count": null, "params": [], "start": **********.788284, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/bottom.blade.phpshop::components.layouts.header.desktop.bottom", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Fbottom.blade.php&line=1", "ajax": false, "filename": "bottom.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.header.desktop.bottom"}, {"name": "2x shop::search.images.index", "param_count": null, "params": [], "start": **********.801281, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/search/images/index.blade.phpshop::search.images.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fsearch%2Fimages%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::search.images.index"}, {"name": "2x shop::checkout.cart.mini-cart", "param_count": null, "params": [], "start": **********.807744, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/checkout/cart/mini-cart.blade.phpshop::checkout.cart.mini-cart", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcheckout%2Fcart%2Fmini-cart.blade.php&line=1", "ajax": false, "filename": "mini-cart.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::checkout.cart.mini-cart"}, {"name": "1x shop::components.quantity-changer.index", "param_count": null, "params": [], "start": **********.814931, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/quantity-changer/index.blade.phpshop::components.quantity-changer.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fquantity-changer%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.quantity-changer.index"}, {"name": "5x shop::components.drawer.index", "param_count": null, "params": [], "start": **********.815704, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/drawer/index.blade.phpshop::components.drawer.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fdrawer%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 5, "name_original": "shop::components.drawer.index"}, {"name": "2x marketplace::components.shop.layouts.header.sell", "param_count": null, "params": [], "start": **********.820137, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src/resources/views/components/shop/layouts/header/sell.blade.phpmarketplace::components.shop.layouts.header.sell", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshop%2Flayouts%2Fheader%2Fsell.blade.php&line=1", "ajax": false, "filename": "sell.blade.php", "line": "?"}, "render_count": 2, "name_original": "marketplace::components.shop.layouts.header.sell"}, {"name": "2x marketplace::components.shop.layouts.header.profile", "param_count": null, "params": [], "start": **********.822634, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src/resources/views/components/shop/layouts/header/profile.blade.phpmarketplace::components.shop.layouts.header.profile", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshop%2Flayouts%2Fheader%2Fprofile.blade.php&line=1", "ajax": false, "filename": "profile.blade.php", "line": "?"}, "render_count": 2, "name_original": "marketplace::components.shop.layouts.header.profile"}, {"name": "1x shop::components.layouts.header.mobile.index", "param_count": null, "params": [], "start": **********.826174, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.phpshop::components.layouts.header.mobile.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fmobile%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.header.mobile.index"}, {"name": "1x shop::components.layouts.services", "param_count": null, "params": [], "start": **********.85269, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/services.blade.phpshop::components.layouts.services", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fservices.blade.php&line=1", "ajax": false, "filename": "services.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.services"}, {"name": "1x shop::components.layouts.footer.index", "param_count": null, "params": [], "start": **********.863504, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/footer/index.blade.phpshop::components.layouts.footer.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Ffooter%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.footer.index"}, {"name": "1x shop::components.accordion.index", "param_count": null, "params": [], "start": **********.882882, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/accordion/index.blade.phpshop::components.accordion.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Faccordion%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.accordion.index"}, {"name": "1x shop::components.form.control-group.control", "param_count": null, "params": [], "start": **********.887784, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/form/control-group/control.blade.phpshop::components.form.control-group.control", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Fcontrol.blade.php&line=1", "ajax": false, "filename": "control.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.form.control-group.control"}, {"name": "1x shop::components.form.control-group.error", "param_count": null, "params": [], "start": **********.888602, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/form/control-group/error.blade.phpshop::components.form.control-group.error", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.form.control-group.error"}, {"name": "1x shop::components.form.index", "param_count": null, "params": [], "start": **********.888978, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/form/index.blade.phpshop::components.form.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.form.index"}, {"name": "1x core::blade.tracer.style", "param_count": null, "params": [], "start": **********.889593, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src/resources/views/blade/tracer/style.blade.phpcore::blade.tracer.style", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FCore%2Fsrc%2FResources%2Fviews%2Fblade%2Ftracer%2Fstyle.blade.php&line=1", "ajax": false, "filename": "style.blade.php", "line": "?"}, "render_count": 1, "name_original": "core::blade.tracer.style"}, {"name": "1x paypal::checkout.onepage.paypal-smart-button", "param_count": null, "params": [], "start": **********.889927, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Paypal\\src/resources/views/checkout/onepage/paypal-smart-button.blade.phppaypal::checkout.onepage.paypal-smart-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FPaypal%2Fsrc%2FResources%2Fviews%2Fcheckout%2Fonepage%2Fpaypal-smart-button.blade.php&line=1", "ajax": false, "filename": "paypal-smart-button.blade.php", "line": "?"}, "render_count": 1, "name_original": "paypal::checkout.onepage.paypal-smart-button"}]}, "queries": {"count": 19, "nb_statements": 19, "nb_visible_statements": 19, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.013770000000000001, "accumulated_duration_str": "13.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "installer_locale", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "theme", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Theme.php", "line": 32}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.599284, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mditems", "explain": null, "start_percent": 0, "width_percent": 4.866}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "currency", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "installer_locale", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 40}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.603055, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mditems", "explain": null, "start_percent": 4.866, "width_percent": 4.866}, {"sql": "select * from `visits` where (`method` = 'GET' and `url` = 'http://127.0.0.1:8000/storage/channel/1/ghBTCHgGKvmgiRQ6BP8w0XynTgOkkSQiXx69toiA.png' and `ip` = '127.0.0.1' and `visitor_id` is null and `visitor_type` is null and `channel_id` = 1) order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["GET", "http://127.0.0.1:8000/storage/channel/1/ghBTCHgGKvmgiRQ6BP8w0XynTgOkkSQiXx69toiA.png", "127.0.0.1", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Jobs/UpdateCreateVisitIndex.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Jobs\\UpdateCreateVisitIndex.php", "line": 45}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.6254342, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "UpdateCreateVisitIndex.php:45", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Jobs/UpdateCreateVisitIndex.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Jobs\\UpdateCreateVisitIndex.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FCore%2Fsrc%2FJobs%2FUpdateCreateVisitIndex.php&line=45", "ajax": false, "filename": "UpdateCreateVisitIndex.php", "line": "45"}, "connection": "mditems", "explain": null, "start_percent": 9.731, "width_percent": 11.038}, {"sql": "select * from `theme_customizations` where `status` = 1 and `channel_id` = 1 order by `sort_order` asc", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Marketplace/src/Http/Controllers/Shop/ProductsCategoriesProxyController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Http\\Controllers\\Shop\\ProductsCategoriesProxyController.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.628933, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 20.77, "width_percent": 7.48}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 22, "namespace": null, "name": "packages/Webkul/Marketplace/src/Http/Controllers/Shop/ProductsCategoriesProxyController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Http\\Controllers\\Shop\\ProductsCategoriesProxyController.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.6325052, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 28.25, "width_percent": 7.044}, {"sql": "select * from `channel_translations` where `channel_translations`.`channel_id` = 1 and `channel_translations`.`channel_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": "view", "name": "shop::home.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/home/<USER>", "line": 9}], "start": **********.641777, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mditems", "explain": null, "start_percent": 35.294, "width_percent": 5.011}, {"sql": "select count(*) as aggregate from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "shop::components.layouts.header.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/index.blade.php", "line": 4}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.77121, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.index:4", "source": {"index": 19, "namespace": "view", "name": "shop::components.layouts.header.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/index.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Findex.blade.php&line=4", "ajax": false, "filename": "index.blade.php", "line": "4"}, "connection": "mditems", "explain": null, "start_percent": 40.305, "width_percent": 4.285}, {"sql": "select count(*) as aggregate from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "shop::components.layouts.header.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/index.blade.php", "line": 4}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.773521, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.index:4", "source": {"index": 19, "namespace": "view", "name": "shop::components.layouts.header.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/index.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Findex.blade.php&line=4", "ajax": false, "filename": "index.blade.php", "line": "4"}, "connection": "mditems", "explain": null, "start_percent": 44.59, "width_percent": 3.05}, {"sql": "select `name` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 and `code` = 'en' order by `name` asc limit 1", "type": "query", "params": [], "bindings": [1, "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "shop::components.layouts.header.desktop.top", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/top.blade.php", "line": 162}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.782568, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.desktop.top:162", "source": {"index": 20, "namespace": "view", "name": "shop::components.layouts.header.desktop.top", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/top.blade.php", "line": 162}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Ftop.blade.php&line=162", "ajax": false, "filename": "top.blade.php", "line": "162"}, "connection": "mditems", "explain": null, "start_percent": 47.64, "width_percent": 5.374}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 order by `name` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "shop::components.layouts.header.desktop.top", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/top.blade.php", "line": 270}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.785605, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.desktop.top:270", "source": {"index": 15, "namespace": "view", "name": "shop::components.layouts.header.desktop.top", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/top.blade.php", "line": 270}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Ftop.blade.php&line=270", "ajax": false, "filename": "top.blade.php", "line": "270"}, "connection": "mditems", "explain": null, "start_percent": 53.014, "width_percent": 3.631}, {"sql": "select * from `attributes` where `attributes`.`id` = 33 limit 1", "type": "query", "params": [], "bindings": [33], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "shop::components.layouts.header.desktop.bottom", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/bottom.blade.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.789199, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.desktop.bottom:44", "source": {"index": 20, "namespace": "view", "name": "shop::components.layouts.header.desktop.bottom", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/bottom.blade.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Fbottom.blade.php&line=44", "ajax": false, "filename": "bottom.blade.php", "line": "44"}, "connection": "mditems", "explain": null, "start_percent": 56.645, "width_percent": 3.558}, {"sql": "select * from `attribute_options` where `attribute_id` = 33", "type": "query", "params": [], "bindings": [33], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "shop::components.layouts.header.desktop.bottom", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/bottom.blade.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.790888, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.desktop.bottom:47", "source": {"index": 15, "namespace": "view", "name": "shop::components.layouts.header.desktop.bottom", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/bottom.blade.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Fbottom.blade.php&line=47", "ajax": false, "filename": "bottom.blade.php", "line": "47"}, "connection": "mditems", "explain": null, "start_percent": 60.203, "width_percent": 3.341}, {"sql": "select count(*) as aggregate from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "shop::components.layouts.header.mobile.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.php", "line": 359}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.839912, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.mobile.index:359", "source": {"index": 19, "namespace": "view", "name": "shop::components.layouts.header.mobile.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.php", "line": 359}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fmobile%2Findex.blade.php&line=359", "ajax": false, "filename": "index.blade.php", "line": "359"}, "connection": "mditems", "explain": null, "start_percent": 63.544, "width_percent": 3.776}, {"sql": "select count(*) as aggregate from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "shop::components.layouts.header.mobile.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.php", "line": 359}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.841722, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.mobile.index:359", "source": {"index": 19, "namespace": "view", "name": "shop::components.layouts.header.mobile.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.php", "line": 359}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fmobile%2Findex.blade.php&line=359", "ajax": false, "filename": "index.blade.php", "line": "359"}, "connection": "mditems", "explain": null, "start_percent": 67.32, "width_percent": 4.212}, {"sql": "select `name` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 and `code` = 'en' order by `name` asc limit 1", "type": "query", "params": [], "bindings": [1, "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "shop::components.layouts.header.mobile.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.php", "line": 442}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.8441348, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.mobile.index:442", "source": {"index": 20, "namespace": "view", "name": "shop::components.layouts.header.mobile.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.php", "line": 442}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fmobile%2Findex.blade.php&line=442", "ajax": false, "filename": "index.blade.php", "line": "442"}, "connection": "mditems", "explain": null, "start_percent": 71.532, "width_percent": 5.737}, {"sql": "select * from `theme_customizations` where `type` = 'services_content' and `status` = 1 and `theme_code` = 'default' and `channel_id` = 1", "type": "query", "params": [], "bindings": ["services_content", 1, "default", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": "view", "name": "shop::components.layouts.services", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/services.blade.php", "line": 13}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.853264, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 77.269, "width_percent": 4.648}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (12)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 23, "namespace": "view", "name": "shop::components.layouts.services", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/services.blade.php", "line": 13}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.8561091, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 81.917, "width_percent": 7.335}, {"sql": "select * from `theme_customizations` where `type` = 'footer_links' and `status` = 1 and `theme_code` = 'default' and `channel_id` = 1", "type": "query", "params": [], "bindings": ["footer_links", 1, "default", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": "view", "name": "shop::components.layouts.footer.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/footer/index.blade.php", "line": 17}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.864247, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 89.252, "width_percent": 6.536}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 23, "namespace": "view", "name": "shop::components.layouts.footer.index", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/footer/index.blade.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.86673, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 95.788, "width_percent": 4.212}]}, "models": {"data": {"Webkul\\Theme\\Models\\ThemeCustomization": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FTheme%2Fsrc%2FModels%2FThemeCustomization.php&line=1", "ajax": false, "filename": "ThemeCustomization.php", "line": "?"}}, "Webkul\\Theme\\Models\\ThemeCustomizationTranslation": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FTheme%2Fsrc%2FModels%2FThemeCustomizationTranslation.php&line=1", "ajax": false, "filename": "ThemeCustomizationTranslation.php", "line": "?"}}, "Webkul\\Core\\Models\\Locale": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeOption": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeOption.php&line=1", "ajax": false, "filename": "AttributeOption.php", "line": "?"}}, "Webkul\\Core\\Models\\Currency": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Webkul\\Marketplace\\Models\\Core\\Visit": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FModels%2FCore%2FVisit.php&line=1", "ajax": false, "filename": "Visit.php", "line": "?"}}, "Webkul\\Core\\Models\\ChannelTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannelTranslation.php&line=1", "ajax": false, "filename": "ChannelTranslation.php", "line": "?"}}, "Webkul\\Attribute\\Models\\Attribute": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}}, "count": 42, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/storage/channel/1/ghBTCHgGKvmgiRQ6BP8w0XynTgOkkSQiXx69toiA.png", "action_name": "shop.product_or_category.index", "controller_action": "Webkul\\Marketplace\\Http\\Controllers\\Shop\\ProductsCategoriesProxyController@index", "uri": "GET {fallbackPlaceholder}", "controller": "Webkul\\Marketplace\\Http\\Controllers\\Shop\\ProductsCategoriesProxyController@index<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FHttp%2FControllers%2FShop%2FProductsCategoriesProxyController.php&line=47\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FHttp%2FControllers%2FShop%2FProductsCategoriesProxyController.php&line=47\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Marketplace/src/Http/Controllers/Shop/ProductsCategoriesProxyController.php:47-156</a>", "middleware": "web, theme, locale, currency, marketplace, cache.response", "duration": "582ms", "peak_memory": "48MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1355156626 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1355156626\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1770605676 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1770605676\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2089243403 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"96 characters\">http://127.0.0.1:8000/california-gold-nutrition-sport-creatine-monohydrate-unflavored-1-lb-454-g</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"748 characters\">_gcl_au=1.1.1178683659.1752735529; XSRF-TOKEN=eyJpdiI6Ii9DSXpCVHBhbjV6WWdjc2pudklENlE9PSIsInZhbHVlIjoiSEVpSzI1d2lHY3VFQjE1cVdpczAwRG1YcFlHL3U5TU9JYjVzZTFlczAxNE9LOStUUjhiWEF0R2UvSllYQld0d1RaVUF4MDdjN3VRWm5WQTdQeEtnQ0IwT3pKOGw4dzdBdzUzdEI1VGx1NURNeUhrUGFpZWp6Z3QwbUhiMnFEVGQiLCJtYWMiOiI5MTVjYWVjMjUwNWYwMzA5ZGM0M2MyNGM3MDkwYWZiODQ5MDExZDRiNTg4NTc4ODg5YzRjNDE4YTU3ZTVlZjNjIiwidGFnIjoiIn0%3D; bagisto_session=eyJpdiI6IjhnVmc0SzlKYWdDV2JLNUk1WUVMK2c9PSIsInZhbHVlIjoiZ1prZmhoME9vS3JtWVJydGt4azBRUFErZWhJZjJuNjIzblZ3SVpBazl1TENXeWcwRVUycFc4aUFmVklscEZhVkJJQlc5elhXci9IQk5EclZycjFyR0oxTHZVTDlyMnJHTGZwdE1XREVERmZ0SVRHSEM1M2pxYWNkTGhsWGVaNmoiLCJtYWMiOiI1Nzg1MzQ0MDM1YjM2M2NiMTk2YjUyYWZkM2QzMjU3OTk3NzgxNWMxNTE2Y2UyOGY4MzA5ZjM1MjIxMTYyMzA3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2089243403\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1635999151 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_gcl_au</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zkGTTvhSKLJlVdv7c7khny3gz8R0QPgdNfWirEBi</span>\"\n  \"<span class=sf-dump-key>bagisto_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">d6NyqSDpTQRmraq0TUwWTC4Y0P4XwPCUN8xQsY9a</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1635999151\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1673977649 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 12:50:22 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1673977649\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1223186945 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zkGTTvhSKLJlVdv7c7khny3gz8R0QPgdNfWirEBi</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"84 characters\">http://127.0.0.1:8000/storage/channel/1/ghBTCHgGKvmgiRQ6BP8w0XynTgOkkSQiXx69toiA.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1223186945\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/storage/channel/1/ghBTCHgGKvmgiRQ6BP8w0XynTgOkkSQiXx69toiA.png", "action_name": "shop.product_or_category.index", "controller_action": "Webkul\\Marketplace\\Http\\Controllers\\Shop\\ProductsCategoriesProxyController@index"}, "badge": null}}