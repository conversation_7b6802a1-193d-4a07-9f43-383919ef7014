{"__meta": {"id": "01K33N7RCRCMH1X83XVQAKK3MC", "datetime": "2025-08-20 17:23:39", "utime": **********.992489, "method": "GET", "uri": "/api/products?sort=name-desc&limit=12&featured=1", "ip": "127.0.0.1"}, "modules": {"count": 5, "modules": [{"name": "Webkul\\Attribute", "models": ["Webkul\\Attribute\\Models\\Attribute (33)", "Webkul\\Attribute\\Models\\AttributeFamily (1)"], "views": [], "queries": [{"sql": "select `id`, `code`, `value_per_channel`, `value_per_locale`, `type`, `is_filterable`, `is_configurable` from `attributes` where `code` in ('sort', 'limit', 'featured', 'channel_id', 'status', 'visible_individually', 'url_key')", "duration": 0.63, "duration_str": "630ms", "connection": "mditems"}, {"sql": "select * from `attributes` where `code` = 'name'", "duration": 1.42, "duration_str": "1.42s", "connection": "mditems"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (1)", "duration": 0.57, "duration_str": "570ms", "connection": "mditems"}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 1", "duration": 1.64, "duration_str": "1.64s", "connection": "mditems"}]}, {"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\Locale (1)", "Webkul\\Core\\Models\\Currency (2)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('127.0.0.1:8000', 'http://127.0.0.1:8000', 'https://127.0.0.1:8000')", "duration": 0.67, "duration_str": "670ms", "connection": "mditems"}, {"sql": "select * from `channels` limit 1", "duration": 0.53, "duration_str": "530ms", "connection": "mditems"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 1.17, "duration_str": "1.17s", "connection": "mditems"}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "duration": 0.72, "duration_str": "720ms", "connection": "mditems"}, {"sql": "select * from `currency_exchange_rates` where `target_currency` = 1", "duration": 1.15, "duration_str": "1.15s", "connection": "mditems"}]}, {"name": "Webkul\\Customer", "models": ["Webkul\\Customer\\Models\\CustomerGroup (1)"], "views": [], "queries": [{"sql": "select * from `customer_groups` where `code` = 'guest'", "duration": 0.62, "duration_str": "620ms", "connection": "mditems"}]}, {"name": "Webkul\\Product", "models": ["Webkul\\Product\\Models\\ProductImage (5)", "Webkul\\Product\\Models\\ProductAttributeValue (79)", "Webkul\\Product\\Models\\ProductPriceIndex (9)", "Webkul\\Product\\Models\\ProductInventoryIndex (4)"], "views": [], "queries": [{"sql": "select * from `product_images` where `product_images`.`product_id` in (1, 2, 6) order by `position` asc", "duration": 0.49, "duration_str": "490ms", "connection": "mditems"}, {"sql": "select * from `product_videos` where `product_videos`.`product_id` in (1, 2, 6) order by `position` asc", "duration": 0.58, "duration_str": "580ms", "connection": "mditems"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (1, 2, 6)", "duration": 0.74, "duration_str": "740ms", "connection": "mditems"}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` in (1, 2, 6)", "duration": 0.6, "duration_str": "600ms", "connection": "mditems"}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` in (1, 2, 6)", "duration": 1.26, "duration_str": "1.26s", "connection": "mditems"}, {"sql": "select * from `product_reviews` where `product_reviews`.`product_id` in (1, 2, 6)", "duration": 0.59, "duration_str": "590ms", "connection": "mditems"}]}, {"name": "Webkul\\Marketplace", "models": ["Webkul\\Marketplace\\Models\\Product\\Product (3)", "Webkul\\Marketplace\\Models\\Product (2)", "Webkul\\Marketplace\\Models\\Seller (2)"], "views": [], "queries": [{"sql": "select distinct `products`.* from `products` left join `products` as `variants` on COALESCE(variants.parent_id, variants.id) = `products`.`id` left join `product_price_indices` on `products`.`id` = `product_price_indices`.`product_id` and `product_price_indices`.`customer_group_id` = 1 left join `marketplace_products` on `products`.`id` = `marketplace_products`.`product_id` left join `marketplace_sellers` on `marketplace_products`.`marketplace_seller_id` = `marketplace_sellers`.`id` left join `product_channels` on `products`.`id` = `product_channels`.`product_id` left join `product_attribute_values` as `url_key_product_attribute_values` on `products`.`id` = `url_key_product_attribute_values`.`product_id` left join `product_attribute_values` as `visible_individually_product_attribute_values` on `products`.`id` = `visible_individually_product_attribute_values`.`product_id` left join `product_attribute_values` as `status_product_attribute_values` on `products`.`id` = `status_product_attribute_values`.`product_id` left join `product_attribute_values` as `featured_product_attribute_values` on `products`.`id` = `featured_product_attribute_values`.`product_id` and `featured_product_attribute_values`.`attribute_id` = 6 left join `product_attribute_values` as `featured_variant_attribute_values` on `variants`.`id` = `featured_variant_attribute_values`.`product_id` and `featured_variant_attribute_values`.`attribute_id` = 6 left join `product_attribute_values` as `sort_product_attribute_values` on `products`.`id` = `sort_product_attribute_values`.`product_id` and `sort_product_attribute_values`.`attribute_id` = 2 and `sort_product_attribute_values`.`locale` = 'en' where (`marketplace_products`.`id` is null or `marketplace_products`.`is_owner` = 0 or (`marketplace_sellers`.`is_suspended` = 0 and `marketplace_sellers`.`is_approved` = 1 and `marketplace_products`.`is_approved` = 1)) and `product_channels`.`channel_id` = '1' and `url_key_product_attribute_values`.`attribute_id` = 3 and `url_key_product_attribute_values`.`text_value` is not null and `visible_individually_product_attribute_values`.`attribute_id` = 7 and `visible_individually_product_attribute_values`.`boolean_value` = 1 and `status_product_attribute_values`.`attribute_id` = 8 and `status_product_attribute_values`.`boolean_value` = 1 and ((`featured_product_attribute_values`.`boolean_value` in ('1')) or (`featured_variant_attribute_values`.`boolean_value` in ('1'))) group by `products`.`id`, `products`.`id` order by `sort_product_attribute_values`.`text_value` desc limit 12 offset 0", "duration": 33.08, "duration_str": "33.08s", "connection": "mditems"}, {"sql": "select * from `products` where `products`.`parent_id` in (1, 2, 6)", "duration": 0.54, "duration_str": "540ms", "connection": "mditems"}, {"sql": "select * from `marketplace_products` where `product_id` = 6 and `is_owner` = 1", "duration": 0.67, "duration_str": "670ms", "connection": "mditems"}, {"sql": "select * from `marketplace_sellers` where `marketplace_sellers`.`id` = 4 limit 1", "duration": 0.63, "duration_str": "630ms", "connection": "mditems"}, {"sql": "select * from `marketplace_products` where `product_id` = 2 and `is_owner` = 1", "duration": 0.83, "duration_str": "830ms", "connection": "mditems"}, {"sql": "select * from `marketplace_sellers` where `marketplace_sellers`.`id` = 4 limit 1", "duration": 1.56, "duration_str": "1.56s", "connection": "mditems"}, {"sql": "select * from `marketplace_products` where `product_id` = 1 and `is_owner` = 1", "duration": 0.87, "duration_str": "870ms", "connection": "mditems"}]}]}, "messages": {"count": 6, "messages": [{"message": "[17:23:39] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.909057, "xdebug_link": null, "collector": "log"}, {"message": "[17:23:39] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.909194, "xdebug_link": null, "collector": "log"}, {"message": "[17:23:39] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.909282, "xdebug_link": null, "collector": "log"}, {"message": "[17:23:39] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.95381, "xdebug_link": null, "collector": "log"}, {"message": "[17:23:39] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.969964, "xdebug_link": null, "collector": "log"}, {"message": "[17:23:39] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.988653, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.454229, "end": **********.007144, "duration": 0.5529148578643799, "duration_str": "553ms", "measures": [{"label": "Booting", "start": **********.454229, "relative_start": 0, "end": **********.736923, "relative_end": **********.736923, "duration": 0.*****************, "duration_str": "283ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.736935, "relative_start": 0.*****************, "end": **********.007146, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "270ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.75223, "relative_start": 0.****************, "end": **********.755655, "relative_end": **********.755655, "duration": 0.003425121307373047, "duration_str": "3.43ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.909425, "relative_start": 0.*****************, "end": **********.991196, "relative_end": **********.991196, "duration": 0.****************, "duration_str": "81.77ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: shop::products.prices.index", "start": **********.95349, "relative_start": 0.*****************, "end": **********.95349, "relative_end": **********.95349, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.index", "start": **********.969657, "relative_start": 0.***************, "end": **********.969657, "relative_end": **********.969657, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::products.prices.index", "start": **********.988398, "relative_start": 0.5341689586639404, "end": **********.988398, "relative_end": **********.988398, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 46012952, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 3, "nb_templates": 3, "templates": [{"name": "shop::products.prices.index", "param_count": null, "params": [], "start": **********.953446, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/products/prices/index.blade.phpshop::products.prices.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "shop::products.prices.index", "param_count": null, "params": [], "start": **********.969615, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/products/prices/index.blade.phpshop::products.prices.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "shop::products.prices.index", "param_count": null, "params": [], "start": **********.988356, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src/resources/views/products/prices/index.blade.phpshop::products.prices.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}]}, "queries": {"count": 22, "nb_statements": 22, "nb_visible_statements": 22, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.09145, "accumulated_duration_str": "91.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "installer_locale", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "theme", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Theme.php", "line": 32}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.768127, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mditems", "explain": null, "start_percent": 0, "width_percent": 1.279}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "currency", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "installer_locale", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 40}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.77174, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mditems", "explain": null, "start_percent": 1.279, "width_percent": 0.787}, {"sql": "select * from `customer_groups` where `code` = 'guest'", "type": "query", "params": [], "bindings": ["guest"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Core.php", "line": 746}, {"index": 19, "namespace": null, "name": "packages/Webkul/Customer/src/Repositories/CustomerRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Customer\\src\\Repositories\\CustomerRepository.php", "line": 41}], "start": **********.788201, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "mditems", "explain": null, "start_percent": 2.067, "width_percent": 0.678}, {"sql": "select `id`, `code`, `value_per_channel`, `value_per_locale`, `type`, `is_filterable`, `is_configurable` from `attributes` where `code` in ('sort', 'limit', 'featured', 'channel_id', 'status', 'visible_individually', 'url_key')", "type": "query", "params": [], "bindings": ["sort", "limit", "featured", "channel_id", "status", "visible_individually", "url_key"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeRepository.php", "line": 198}, {"index": 17, "namespace": null, "name": "packages/Webkul/Marketplace/src/Repositories/Product/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Repositories\\Product\\ProductRepository.php", "line": 109}, {"index": 18, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 1033}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 485}], "start": **********.7927291, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mditems", "explain": null, "start_percent": 2.745, "width_percent": 0.689}, {"sql": "select * from `attributes` where `code` = 'name'", "type": "query", "params": [], "bindings": ["name"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/Marketplace/src/Repositories/Product/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Repositories\\Product\\ProductRepository.php", "line": 200}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 1033}], "start": **********.797591, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "mditems", "explain": null, "start_percent": 3.434, "width_percent": 1.553}, {"sql": "select count(*) as aggregate from (select distinct `products`.* from `products` left join `products` as `variants` on COALESCE(variants.parent_id, variants.id) = `products`.`id` left join `product_price_indices` on `products`.`id` = `product_price_indices`.`product_id` and `product_price_indices`.`customer_group_id` = 1 left join `marketplace_products` on `products`.`id` = `marketplace_products`.`product_id` left join `marketplace_sellers` on `marketplace_products`.`marketplace_seller_id` = `marketplace_sellers`.`id` left join `product_channels` on `products`.`id` = `product_channels`.`product_id` left join `product_attribute_values` as `url_key_product_attribute_values` on `products`.`id` = `url_key_product_attribute_values`.`product_id` left join `product_attribute_values` as `visible_individually_product_attribute_values` on `products`.`id` = `visible_individually_product_attribute_values`.`product_id` left join `product_attribute_values` as `status_product_attribute_values` on `products`.`id` = `status_product_attribute_values`.`product_id` left join `product_attribute_values` as `featured_product_attribute_values` on `products`.`id` = `featured_product_attribute_values`.`product_id` and `featured_product_attribute_values`.`attribute_id` = 6 left join `product_attribute_values` as `featured_variant_attribute_values` on `variants`.`id` = `featured_variant_attribute_values`.`product_id` and `featured_variant_attribute_values`.`attribute_id` = 6 left join `product_attribute_values` as `sort_product_attribute_values` on `products`.`id` = `sort_product_attribute_values`.`product_id` and `sort_product_attribute_values`.`attribute_id` = 2 and `sort_product_attribute_values`.`locale` = 'en' where (`marketplace_products`.`id` is null or `marketplace_products`.`is_owner` = 0 or (`marketplace_sellers`.`is_suspended` = 0 and `marketplace_sellers`.`is_approved` = 1 and `marketplace_products`.`is_approved` = 1)) and `product_channels`.`channel_id` = '1' and `url_key_product_attribute_values`.`attribute_id` = 3 and `url_key_product_attribute_values`.`text_value` is not null and `visible_individually_product_attribute_values`.`attribute_id` = 7 and `visible_individually_product_attribute_values`.`boolean_value` = 1 and `status_product_attribute_values`.`attribute_id` = 8 and `status_product_attribute_values`.`boolean_value` = 1 and ((`featured_product_attribute_values`.`boolean_value` in ('1')) or (`featured_variant_attribute_values`.`boolean_value` in ('1'))) group by `products`.`id`, `products`.`id`) as `aggregate_table`", "type": "query", "params": [], "bindings": [1, 6, 6, 2, "en", 0, 0, 1, 1, "1", 3, 7, 1, 8, 1, "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 17, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 18, "namespace": null, "name": "packages/Webkul/Marketplace/src/Repositories/Product/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Repositories\\Product\\ProductRepository.php", "line": 240}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 215}, {"index": 20, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.803398, "duration": 0.04109, "duration_str": "41.09ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mditems", "explain": null, "start_percent": 4.986, "width_percent": 44.932}, {"sql": "select distinct `products`.* from `products` left join `products` as `variants` on COALESCE(variants.parent_id, variants.id) = `products`.`id` left join `product_price_indices` on `products`.`id` = `product_price_indices`.`product_id` and `product_price_indices`.`customer_group_id` = 1 left join `marketplace_products` on `products`.`id` = `marketplace_products`.`product_id` left join `marketplace_sellers` on `marketplace_products`.`marketplace_seller_id` = `marketplace_sellers`.`id` left join `product_channels` on `products`.`id` = `product_channels`.`product_id` left join `product_attribute_values` as `url_key_product_attribute_values` on `products`.`id` = `url_key_product_attribute_values`.`product_id` left join `product_attribute_values` as `visible_individually_product_attribute_values` on `products`.`id` = `visible_individually_product_attribute_values`.`product_id` left join `product_attribute_values` as `status_product_attribute_values` on `products`.`id` = `status_product_attribute_values`.`product_id` left join `product_attribute_values` as `featured_product_attribute_values` on `products`.`id` = `featured_product_attribute_values`.`product_id` and `featured_product_attribute_values`.`attribute_id` = 6 left join `product_attribute_values` as `featured_variant_attribute_values` on `variants`.`id` = `featured_variant_attribute_values`.`product_id` and `featured_variant_attribute_values`.`attribute_id` = 6 left join `product_attribute_values` as `sort_product_attribute_values` on `products`.`id` = `sort_product_attribute_values`.`product_id` and `sort_product_attribute_values`.`attribute_id` = 2 and `sort_product_attribute_values`.`locale` = 'en' where (`marketplace_products`.`id` is null or `marketplace_products`.`is_owner` = 0 or (`marketplace_sellers`.`is_suspended` = 0 and `marketplace_sellers`.`is_approved` = 1 and `marketplace_products`.`is_approved` = 1)) and `product_channels`.`channel_id` = '1' and `url_key_product_attribute_values`.`attribute_id` = 3 and `url_key_product_attribute_values`.`text_value` is not null and `visible_individually_product_attribute_values`.`attribute_id` = 7 and `visible_individually_product_attribute_values`.`boolean_value` = 1 and `status_product_attribute_values`.`attribute_id` = 8 and `status_product_attribute_values`.`boolean_value` = 1 and ((`featured_product_attribute_values`.`boolean_value` in ('1')) or (`featured_variant_attribute_values`.`boolean_value` in ('1'))) group by `products`.`id`, `products`.`id` order by `sort_product_attribute_values`.`text_value` desc limit 12 offset 0", "type": "query", "params": [], "bindings": [1, 6, 6, 2, "en", 0, 0, 1, 1, "1", 3, 7, 1, 8, 1, "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 17, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 18, "namespace": null, "name": "packages/Webkul/Marketplace/src/Repositories/Product/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Repositories\\Product\\ProductRepository.php", "line": 240}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 215}, {"index": 20, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.848126, "duration": 0.03308, "duration_str": "33.08ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mditems", "explain": null, "start_percent": 49.918, "width_percent": 36.173}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Marketplace/src/Repositories/Product/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Repositories\\Product\\ProductRepository.php", "line": 240}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 215}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.885311, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mditems", "explain": null, "start_percent": 86.091, "width_percent": 0.623}, {"sql": "select * from `product_images` where `product_images`.`product_id` in (1, 2, 6) order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Marketplace/src/Repositories/Product/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Repositories\\Product\\ProductRepository.php", "line": 240}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 215}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.888169, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mditems", "explain": null, "start_percent": 86.714, "width_percent": 0.536}, {"sql": "select * from `product_videos` where `product_videos`.`product_id` in (1, 2, 6) order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Marketplace/src/Repositories/Product/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Repositories\\Product\\ProductRepository.php", "line": 240}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 215}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.890272, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mditems", "explain": null, "start_percent": 87.25, "width_percent": 0.634}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (1, 2, 6)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Marketplace/src/Repositories/Product/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Repositories\\Product\\ProductRepository.php", "line": 240}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 215}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.892258, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mditems", "explain": null, "start_percent": 87.884, "width_percent": 0.809}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` in (1, 2, 6)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Marketplace/src/Repositories/Product/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Repositories\\Product\\ProductRepository.php", "line": 240}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 215}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.8955588, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mditems", "explain": null, "start_percent": 88.693, "width_percent": 0.656}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` in (1, 2, 6)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Marketplace/src/Repositories/Product/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Repositories\\Product\\ProductRepository.php", "line": 240}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 215}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.898844, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mditems", "explain": null, "start_percent": 89.349, "width_percent": 1.378}, {"sql": "select * from `product_reviews` where `product_reviews`.`product_id` in (1, 2, 6)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Marketplace/src/Repositories/Product/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Repositories\\Product\\ProductRepository.php", "line": 240}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 215}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.902597, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mditems", "explain": null, "start_percent": 90.727, "width_percent": 0.645}, {"sql": "select * from `products` where `products`.`parent_id` in (1, 2, 6)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Marketplace/src/Repositories/Product/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Repositories\\Product\\ProductRepository.php", "line": 240}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 215}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 34}], "start": **********.904633, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mditems", "explain": null, "start_percent": 91.372, "width_percent": 0.59}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeRepository.php", "line": 213}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 514}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}], "start": **********.9173589, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "AttributeFamily.php:53", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=53", "ajax": false, "filename": "AttributeFamily.php", "line": "53"}, "connection": "mditems", "explain": null, "start_percent": 91.963, "width_percent": 1.793}, {"sql": "select * from `marketplace_products` where `product_id` = 6 and `is_owner` = 1", "type": "query", "params": [], "bindings": [6, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": null, "name": "packages/Webkul/Marketplace/src/Types/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Types\\Simple.php", "line": 26}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 505}], "start": **********.941164, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 93.756, "width_percent": 0.733}, {"sql": "select * from `marketplace_sellers` where `marketplace_sellers`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Marketplace/src/Types/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Types\\Simple.php", "line": 33}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 505}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 157}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 145}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 44}], "start": **********.943308, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Simple.php:33", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Marketplace/src/Types/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Types\\Simple.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FTypes%2FSimple.php&line=33", "ajax": false, "filename": "Simple.php", "line": "33"}, "connection": "mditems", "explain": null, "start_percent": 94.489, "width_percent": 0.689}, {"sql": "select * from `currency_exchange_rates` where `target_currency` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Core.php", "line": 457}, {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Core.php", "line": 479}], "start": **********.947457, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 95.178, "width_percent": 1.258}, {"sql": "select * from `marketplace_products` where `product_id` = 2 and `is_owner` = 1", "type": "query", "params": [], "bindings": [2, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": null, "name": "packages/Webkul/Marketplace/src/Types/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Types\\Simple.php", "line": 26}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 505}], "start": **********.9604912, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 96.435, "width_percent": 0.908}, {"sql": "select * from `marketplace_sellers` where `marketplace_sellers`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Marketplace/src/Types/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Types\\Simple.php", "line": 33}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 505}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 157}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 145}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Resources/ProductResource.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php", "line": 44}], "start": **********.964402, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "Simple.php:33", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Marketplace/src/Types/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Types\\Simple.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FTypes%2FSimple.php&line=33", "ajax": false, "filename": "Simple.php", "line": "33"}, "connection": "mditems", "explain": null, "start_percent": 97.343, "width_percent": 1.706}, {"sql": "select * from `marketplace_products` where `product_id` = 1 and `is_owner` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": null, "name": "packages/Webkul/Marketplace/src/Types/Simple.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Marketplace\\src\\Types\\Simple.php", "line": 26}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "C:\\xampp\\htdocs\\New Mditems\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 505}], "start": **********.976483, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "C:\\xampp\\htdocs\\New Mditems\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mditems", "explain": null, "start_percent": 99.049, "width_percent": 0.951}]}, "models": {"data": {"Webkul\\Product\\Models\\ProductAttributeValue": {"value": 79, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductAttributeValue.php&line=1", "ajax": false, "filename": "ProductAttributeValue.php", "line": "?"}}, "Webkul\\Attribute\\Models\\Attribute": {"value": 33, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductPriceIndex": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductPriceIndex.php&line=1", "ajax": false, "filename": "ProductPriceIndex.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductImage": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductImage.php&line=1", "ajax": false, "filename": "ProductImage.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductInventoryIndex": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductInventoryIndex.php&line=1", "ajax": false, "filename": "ProductInventoryIndex.php", "line": "?"}}, "Webkul\\Marketplace\\Models\\Product\\Product": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FModels%2FProduct%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Webkul\\Core\\Models\\Currency": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Webkul\\Marketplace\\Models\\Product": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Webkul\\Marketplace\\Models\\Seller": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FMarketplace%2Fsrc%2FModels%2FSeller.php&line=1", "ajax": false, "filename": "Seller.php", "line": "?"}}, "Webkul\\Core\\Models\\Locale": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Customer\\Models\\CustomerGroup": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FCustomer%2Fsrc%2FModels%2FCustomerGroup.php&line=1", "ajax": false, "filename": "CustomerGroup.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeFamily": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=1", "ajax": false, "filename": "AttributeFamily.php", "line": "?"}}}, "count": 142, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/api/products?featured=1&limit=12&sort=name-desc", "action_name": "shop.api.products.index", "controller_action": "Webkul\\Shop\\Http\\Controllers\\API\\ProductController@index", "uri": "GET api/products", "controller": "Webkul\\Shop\\Http\\Controllers\\API\\ProductController@index<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FAPI%2FProductController.php&line=26\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/products", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FNew%20Mditems%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FAPI%2FProductController.php&line=26\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php:26-56</a>", "middleware": "web, shop, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance", "duration": "558ms", "peak_memory": "46MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1226008307 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sort</span>\" => \"<span class=sf-dump-str title=\"9 characters\">name-desc</span>\"\n  \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str title=\"2 characters\">12</span>\"\n  \"<span class=sf-dump-key>featured</span>\" => \"<span class=sf-dump-str>1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1226008307\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2172097 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2172097\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-297693161 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InRSQStQR09WNnlRdVJidjZjdXNKbkE9PSIsInZhbHVlIjoidkFhbmltMExuempMcTM4MnNydGtJRTFCNkxKSFpiaWc1S2lrcjdQVzFhUG5IdmlnTFBTMDA2dk5DS2U4NnBlR0V5b2tEeXJReG1hQUcxK1dCYy9EUXNnMWVacUZIQnZVVWMxeFJrWXZxc2o2YkRicGdYZkkvSkF3STVZWTJxNFgiLCJtYWMiOiJlZDIzYWYwZmE5NDIyODIwMGJmMzQ4NjExNDlmY2U0ZDAzNzEzMzcyOTUxNjM2NDJlMjNmYWE4NzdiNDhiMzVmIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"748 characters\">_gcl_au=1.1.1178683659.1752735529; XSRF-TOKEN=eyJpdiI6InRSQStQR09WNnlRdVJidjZjdXNKbkE9PSIsInZhbHVlIjoidkFhbmltMExuempMcTM4MnNydGtJRTFCNkxKSFpiaWc1S2lrcjdQVzFhUG5IdmlnTFBTMDA2dk5DS2U4NnBlR0V5b2tEeXJReG1hQUcxK1dCYy9EUXNnMWVacUZIQnZVVWMxeFJrWXZxc2o2YkRicGdYZkkvSkF3STVZWTJxNFgiLCJtYWMiOiJlZDIzYWYwZmE5NDIyODIwMGJmMzQ4NjExNDlmY2U0ZDAzNzEzMzcyOTUxNjM2NDJlMjNmYWE4NzdiNDhiMzVmIiwidGFnIjoiIn0%3D; bagisto_session=eyJpdiI6InRpd3FVb3A5dUtNcEcrVUxGNmh0Q1E9PSIsInZhbHVlIjoiUzhoN1U0b0ZYTElsSHBITTA1THNQeHNubHFwKzRIaWlsS0hBQTVsZUVJWnVwUklNNklVS0UzS1JkZHN0cUtLQ29POC9KRzVieGovbXpyUDdBVjhudGJ6blgxS2lNNytxRGZZZXNpZk5IQ2hub2twbWMzU1ExNk9qUEdEVmlvYkUiLCJtYWMiOiIwZDlhNmNmMjA5OTYyNDAyMWMwYmMxYjU1MzJmZTg0ODBjMjRhZmU1NDQ5YmE3MmJmYjk3N2IxNDM2MmE5YjMwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-297693161\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1641352248 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_gcl_au</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Rg5UXRNM1Rudt0uHTp3RzWh1ttA0AIVwpo8a7mth</span>\"\n  \"<span class=sf-dump-key>bagisto_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LMGWaDWrQr5haeO2Vdq8IZpsMreeleVKGkdH5G60</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1641352248\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1989105114 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 20 Aug 2025 11:53:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1989105114\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-521285662 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Rg5UXRNM1Rudt0uHTp3RzWh1ttA0AIVwpo8a7mth</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"84 characters\">http://127.0.0.1:8000/storage/channel/1/ghBTCHgGKvmgiRQ6BP8w0XynTgOkkSQiXx69toiA.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-521285662\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/api/products?featured=1&limit=12&sort=name-desc", "action_name": "shop.api.products.index", "controller_action": "Webkul\\Shop\\Http\\Controllers\\API\\ProductController@index"}, "badge": null}}